# Form Implementation Documentation

This document provides a detailed explanation of how forms are implemented in the Verbrauchsausweis application using TanStack React Form.

## Table of Contents

1. [Overview](#overview)
2. [Technology Stack](#technology-stack)
3. [Form Architecture](#form-architecture)
4. [Form Validation](#form-validation)
5. [Form Components](#form-components)
6. [Data Submission](#data-submission)
7. [<PERSON><PERSON><PERSON>ling](#error-handling)
8. [Example Implementation](#example-implementation)
9. [Best Practices](#best-practices)

## Overview

The Verbrauchsausweis application uses a multi-step form approach to collect detailed information about buildings for energy certificate generation. The forms are implemented using TanStack React Form (formerly React Form), which provides a type-safe, performant, and flexible solution for form management in React applications.

## Technology Stack

The form implementation relies on the following technologies:

- **TanStack React Form** (`@tanstack/react-form`): Core form management library
- **Zod** (`zod`): Schema validation library
- **TanStack React Query** (`@tanstack/react-query`): Data fetching and state management
- **Supabase** (`@supabase/supabase-js`): Backend service for data storage
- **TypeScript**: For type safety throughout the application
- **Tailwind CSS**: For styling form components

## Form Architecture

### Form Structure

Each form in the application follows a consistent pattern:

1. **Schema Definition**: Using Zod to define the form schema with validation rules
2. **Form Initialization**: Using TanStack React Form's `useForm` hook
3. **Field Components**: Reusable field components that handle input, validation, and error display
4. **Form Submission**: Handling form submission with React Query mutations

### Multi-Step Form Flow

The application implements a multi-step form process where users navigate through several pages to complete the energy certificate data entry:

1. Objektdaten (Building Information)
2. Additional data forms as required by the application flow
3. Summary page before submission

Each step saves data to Supabase, allowing users to continue their progress later.

## Form Validation

### Validation with Zod

Form validation is implemented using Zod schemas. Each form field has specific validation rules defined in the schema:

```typescript
const objektdatenSchema = z.object({
  // Objektdaten
  ID: z.string().min(1, 'ID ist erforderlich'),
  Strasse: z.string().min(1, 'Straße ist erforderlich'),
  Hausnr: z.string().min(1, 'Hausnummer ist erforderlich'),
  PLZ: z.string().length(5, 'PLZ muss 5 Ziffern haben').regex(/^\d+$/, 'PLZ muss aus Ziffern bestehen'),
  Ort: z.string().min(1, 'Ort ist erforderlich'),
  
  // Additional fields...
});
```

### Validation Triggers

Validation is triggered on:
- Field blur (when a user leaves a field)
- Form submission
- Optionally on field change (for immediate feedback)

## Form Components

### Reusable Form Field Component

The application uses a reusable `FormField` component that encapsulates common form field functionality:

```typescript
const FormField = ({ 
  name, 
  label, 
  type = 'text', 
  placeholder = '', 
  required = true 
}: { 
  name: keyof FormValues; 
  label: string; 
  type?: string; 
  placeholder?: string; 
  required?: boolean;
}) => {
  const { state, handleChange, handleBlur } = useField({
    name,
    form,
  });

  return (
    <div className="mb-4">
      <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <input
        id={name}
        name={name}
        type={type}
        value={state.value ?? ''}
        onChange={(e) => handleChange(e.target.value)}
        onBlur={handleBlur}
        placeholder={placeholder}
        className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
          state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
        }`}
      />
      {state.meta.errors.length > 0 && (
        <p className="mt-1 text-sm text-red-500">{state.meta.errors.join(', ')}</p>
      )}
    </div>
  );
};
```

This component handles:
- Input rendering with appropriate attributes
- Label display with required field indication
- Error message display
- Styling based on validation state

## Data Submission

### React Query Mutations

Form submission is handled using React Query's `useMutation` hook, which provides:
- Loading states
- Error handling
- Success callbacks
- Automatic query invalidation

```typescript
const saveMutation = useMutation({
  mutationFn: async (data: FormValues) => {
    const { data: result, error } = await supabase
      .from('energieausweise')
      .upsert({
        user_id: (await supabase.auth.getUser()).data.user?.id,
        objektdaten: data,
        updated_at: new Date().toISOString(),
      }, {
        onConflict: 'user_id',
      });

    if (error) throw error;
    return result;
  },
  onSuccess: () => {
    // Invalidate queries to refresh data
    queryClient.invalidateQueries({ queryKey: ['energieausweise'] });
    // Navigate to the next page or show success message
    navigate({ to: '/erfassen/objektdaten' });
  },
  onError: (error) => {
    setSubmitError(`Fehler beim Speichern: ${error.message}`);
  },
});
```

### Data Persistence

Form data is persisted to Supabase using the `upsert` method, which:
- Creates a new record if one doesn't exist
- Updates an existing record if one exists (based on conflict resolution)

## Error Handling

The form implementation includes comprehensive error handling:

1. **Validation Errors**: Displayed inline below each field
2. **Submission Errors**: Displayed at the form level
3. **Network Errors**: Handled through React Query's error states

```typescript
{submitError && (
  <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
    {submitError}
  </div>
)}
```

## Example Implementation

Below is a complete example of a form implementation from the ObjektdatenPage:

```typescript
export const ObjektdatenPage = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Define the mutation for saving data to Supabase
  const saveMutation = useMutation({
    mutationFn: async (data: ObjektdatenFormValues) => {
      const { data: result, error } = await supabase
        .from('energieausweise')
        .upsert({
          user_id: (await supabase.auth.getUser()).data.user?.id,
          objektdaten: data,
          updated_at: new Date().toISOString(),
        }, {
          onConflict: 'user_id',
        });

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['energieausweise'] });
      // Navigate to the next page or show success message
      navigate({ to: '/erfassen/objektdaten' });
    },
    onError: (error) => {
      setSubmitError(`Fehler beim Speichern: ${error.message}`);
    },
  });

  // Create the form
  const form = useForm({
    defaultValues: {
      ID: '',
      Strasse: '',
      // Other default values...
    },
    onSubmit: async ({ value }) => {
      setSubmitError(null);
      saveMutation.mutate(value);
    },
  });

  // Form rendering with fields and submission handling
  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Objektdaten erfassen
      </h1>
      
      <form
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          form.handleSubmit();
        }}
        className="bg-white shadow-md rounded-lg p-6"
      >
        {/* Form fields */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
            Objektdaten
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField name="ID" label="ID" placeholder="Eindeutige ID des Gebäudes" />
            {/* Other form fields */}
          </div>
        </div>

        {/* Error display */}
        {submitError && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {submitError}
          </div>
        )}

        {/* Form actions */}
        <div className="flex justify-between mt-8">
          <Link
            to="/meine-zertifikate"
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
          >
            Zurück
          </Link>
          <button
            type="submit"
            disabled={form.state.isSubmitting || saveMutation.isPending}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:bg-green-300"
          >
            {form.state.isSubmitting || saveMutation.isPending ? 'Wird gespeichert...' : 'Speichern'}
          </button>
        </div>
      </form>
    </div>
  );
};
```

## Best Practices

The form implementation follows these best practices:

1. **Type Safety**: Using TypeScript and Zod for type-safe form handling
2. **Reusable Components**: Creating reusable form field components
3. **Validation**: Comprehensive validation with clear error messages
4. **User Experience**: 
   - Clear labeling of required fields
   - Visual feedback for validation states
   - Disabled submit buttons during submission
   - Loading indicators
5. **Error Handling**: Comprehensive error handling at field and form levels
6. **Responsive Design**: Using Tailwind CSS grid system for responsive layouts
7. **Data Persistence**: Saving form data to allow users to continue later

## Conclusion

The form implementation in the Verbrauchsausweis application provides a robust, type-safe, and user-friendly way to collect complex data. By leveraging TanStack React Form, Zod, and React Query, the application achieves a balance of performance, type safety, and user experience.

The modular approach with reusable components makes it easy to maintain and extend the forms as requirements evolve, while the integration with Supabase ensures reliable data persistence.