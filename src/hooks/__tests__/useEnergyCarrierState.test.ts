import { renderHook, act } from '@testing-library/react';
import { useEnergyCarrierState } from '../useEnergyCarrierState';

describe('useEnergyCarrierState', () => {
  it('should initialize with default state', () => {
    const { result } = renderHook(() => useEnergyCarrierState());

    expect(result.current.showEtr2).toBe(false);
    expect(result.current.showEtr3).toBe(false);
    expect(result.current.fernwaermeStates.etr1IsFw).toBe(false);
    expect(result.current.fernwaermeStates.etr2IsFw).toBe(false);
    expect(result.current.fernwaermeStates.etr3IsFw).toBe(false);
  });

  it('should update showEtr2 state', () => {
    const { result } = renderHook(() => useEnergyCarrierState());

    act(() => {
      result.current.setShowEtr2(true);
    });

    expect(result.current.showEtr2).toBe(true);
  });

  it('should update showEtr3 state', () => {
    const { result } = renderHook(() => useEnergyCarrierState());

    act(() => {
      result.current.setShowEtr3(true);
    });

    expect(result.current.showEtr3).toBe(true);
  });

  it('should update fernwärme states', () => {
    const { result } = renderHook(() => useEnergyCarrierState());

    act(() => {
      result.current.setEtr1IsFw(true);
      result.current.setEtr2IsFw(true);
      result.current.setEtr3IsFw(true);
    });

    expect(result.current.fernwaermeStates.etr1IsFw).toBe(true);
    expect(result.current.fernwaermeStates.etr2IsFw).toBe(true);
    expect(result.current.fernwaermeStates.etr3IsFw).toBe(true);
  });

  it('should initialize state from existing data', () => {
    const existingData = {
      verbrauchsdaten: {
        ETr2_Kategorie: 'BK_STROM',
        ETr3_Kategorie: 'BK_FERNWAERME',
        ETr1_isFw: '1',
        ETr2_isFw: '0',
        ETr3_isFw: '1',
      }
    };

    const { result } = renderHook(() => useEnergyCarrierState(existingData));

    expect(result.current.showEtr2).toBe(true);
    expect(result.current.showEtr3).toBe(true);
    expect(result.current.fernwaermeStates.etr1IsFw).toBe(true);
    expect(result.current.fernwaermeStates.etr2IsFw).toBe(false);
    expect(result.current.fernwaermeStates.etr3IsFw).toBe(true);
  });

  it('should update state when existing data changes', () => {
    const initialData = {
      verbrauchsdaten: {
        ETr1_isFw: '0',
      }
    };

    const { result, rerender } = renderHook(
      ({ existingData }) => useEnergyCarrierState(existingData),
      { initialProps: { existingData: initialData } }
    );

    expect(result.current.fernwaermeStates.etr1IsFw).toBe(false);

    const updatedData = {
      verbrauchsdaten: {
        ETr1_isFw: '1',
        ETr2_Kategorie: 'BK_STROM',
      }
    };

    rerender({ existingData: updatedData });

    expect(result.current.fernwaermeStates.etr1IsFw).toBe(true);
    expect(result.current.showEtr2).toBe(true);
  });
});
