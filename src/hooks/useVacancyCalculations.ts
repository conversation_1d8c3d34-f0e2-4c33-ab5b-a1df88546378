import { useCallback } from 'react';

/**
 * Interface for vacancy date validation errors
 */
export interface VacancyDateValidationErrors {
  vonError: string | null;
  bisError: string | null;
}

/**
 * Interface for the vacancy calculations hook return value
 */
export interface VacancyCalculationsReturn {
  calculateLeerstandPercentage: (
    leerstandVon: string,
    leerstandBis: string,
    periodVon: string,
    periodBis: string
  ) => number;
  validateNotFutureDate: (dateString: string) => boolean;
  validateLeerstandDates: (
    leerstandVon: string,
    leerstandBis: string,
    periodVon: string,
    periodBis: string
  ) => VacancyDateValidationErrors;
}

/**
 * Custom hook for vacancy period calculations and validations
 * Provides utilities for calculating vacancy percentages and validating dates
 */
export const useVacancyCalculations = (): VacancyCalculationsReturn => {
  /**
   * Calculate vacancy percentage from date ranges
   * @param leerstandVon - Start date of vacancy period
   * @param leerstandBis - End date of vacancy period
   * @param periodVon - Start date of consumption period
   * @param periodBis - End date of consumption period
   * @returns Calculated percentage rounded to 2 decimal places
   */
  const calculateLeerstandPercentage = useCallback((
    leerstandVon: string,
    leerstandBis: string,
    periodVon: string,
    periodBis: string
  ): number => {
    if (!leerstandVon || !leerstandBis || !periodVon || !periodBis) return 0;

    const leerstandStart = new Date(leerstandVon);
    const leerstandEnd = new Date(leerstandBis);
    const periodStart = new Date(periodVon);
    const periodEnd = new Date(periodBis);

    // Validate dates
    if (isNaN(leerstandStart.getTime()) || isNaN(leerstandEnd.getTime()) ||
        isNaN(periodStart.getTime()) || isNaN(periodEnd.getTime())) {
      return 0;
    }

    // Calculate durations in milliseconds
    const leerstandDuration = leerstandEnd.getTime() - leerstandStart.getTime();
    const totalDuration = periodEnd.getTime() - periodStart.getTime();

    // Ensure positive durations
    if (leerstandDuration <= 0 || totalDuration <= 0) return 0;

    // Calculate percentage and round to 2 decimal places
    const percentage = (leerstandDuration / totalDuration) * 100;
    return Math.round(percentage * 100) / 100;
  }, []);

  /**
   * Validate that a date is not in the future
   * @param dateString - Date string to validate
   * @returns True if date is valid (not in future), false otherwise
   */
  const validateNotFutureDate = useCallback((dateString: string): boolean => {
    if (!dateString) return true; // Empty dates are handled by optional validation

    const inputDate = new Date(dateString);
    const today = new Date();

    // Set today to end of day for comparison (23:59:59)
    today.setHours(23, 59, 59, 999);

    return inputDate <= today;
  }, []);

  /**
   * Validate vacancy period dates against consumption period dates
   * @param leerstandVon - Start date of vacancy period
   * @param leerstandBis - End date of vacancy period
   * @param periodVon - Start date of consumption period
   * @param periodBis - End date of consumption period
   * @returns Object containing validation errors for both dates
   */
  const validateLeerstandDates = useCallback((
    leerstandVon: string,
    leerstandBis: string,
    periodVon: string,
    periodBis: string
  ): VacancyDateValidationErrors => {
    let vonError: string | null = null;
    let bisError: string | null = null;

    // Check for future dates first
    if (leerstandVon && !validateNotFutureDate(leerstandVon)) {
      vonError = 'Das Datum darf nicht in der Zukunft liegen';
    } else if (leerstandVon && periodVon) {
      const leerstandVonDate = new Date(leerstandVon);
      const periodVonDate = new Date(periodVon);

      if (leerstandVonDate < periodVonDate) {
        vonError = 'Das Leerstand-Startdatum kann nicht vor dem Verbrauchszeitraum-Startdatum liegen';
      }
    }

    if (leerstandBis && !validateNotFutureDate(leerstandBis)) {
      bisError = 'Das Datum darf nicht in der Zukunft liegen';
    } else if (leerstandBis && periodBis) {
      const leerstandBisDate = new Date(leerstandBis);
      const periodBisDate = new Date(periodBis);

      if (leerstandBisDate > periodBisDate) {
        bisError = 'Das Leerstand-Enddatum kann nicht nach dem Verbrauchszeitraum-Enddatum liegen';
      }
    }

    if (leerstandVon && leerstandBis && !vonError && !bisError) {
      const leerstandVonDate = new Date(leerstandVon);
      const leerstandBisDate = new Date(leerstandBis);

      if (leerstandVonDate > leerstandBisDate) {
        vonError = 'Das Leerstand-Startdatum muss vor dem Leerstand-Enddatum liegen';
      }
    }

    return { vonError, bisError };
  }, [validateNotFutureDate]);

  return {
    calculateLeerstandPercentage,
    validateNotFutureDate,
    validateLeerstandDates
  };
};
