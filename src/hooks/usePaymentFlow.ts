import { useState, useCallback, useMemo, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';
import { type EnergieausweisData } from '../types/csv';
import { useStableMutation } from './useStableMutation';
import { certificateQueryKeys } from '../utils/queryKeys';

// Define specific error types for better error handling
interface CheckoutError {
  message: string;
  code?: string;
}

export interface PaymentFlowReturn {
  // State
  isProcessingPayment: boolean;
  error: string | null;

  // Actions
  handleCheckout: (legalConsent: any, isAnonymous: boolean, onShowAccountConversion: () => void) => Promise<void>;
  setError: (error: string | null) => void;
  setIsProcessingPayment: (processing: boolean) => void;

  // Mutations (for external access if needed)
  updatePaymentInitiatedMutation: any;
  createCheckoutMutation: any;
}

/**
 * Custom hook for managing payment flow logic
 * Handles payment processing, Stripe integration, status updates, and payment tracking
 */
export const usePaymentFlow = (
  activeCertificateId: string | null,
  energieausweisData: EnergieausweisData | null
): PaymentFlowReturn => {
  const queryClient = useQueryClient();
  const [error, setError] = useState<string | null>(null);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  // Track payment attempt when checkout session is created
  const trackPaymentAttempt = async (sessionId: string, amount: number) => {
    if (!activeCertificateId) return;

    try {
      // Create payment attempt record
      const { error } = await supabase
        .from('payment_attempts')
        .insert({
          certificate_id: activeCertificateId,
          stripe_session_id: sessionId,
          attempt_status: 'initiated',
          amount_cents: amount,
          currency: 'eur',
          user_agent: navigator.userAgent,
          created_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error tracking payment attempt:', error);
      } else {
        console.log('Payment attempt tracked successfully');
      }
    } catch (error) {
      console.error('Error tracking payment attempt:', error);
    }
  };

  // Mutation for creating Stripe checkout session (using stable mutation)
  const createCheckoutMutation = useStableMutation({
    mutationFn: async () => {
      if (!activeCertificateId || !energieausweisData) {
        throw new Error('Keine Daten verfügbar für die Zahlung.');
      }

      // Get current user for email
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('Benutzer nicht eingeloggt.');
      }

      // Generate URLs using the router pattern but still need to use full URLs for Stripe
      const successUrl = `${window.location.origin}/payment-success?certificate_id=${activeCertificateId}`;
      const cancelUrl = `${window.location.origin}/payment-cancel?certificate_id=${activeCertificateId}`;

      // Call the Supabase Edge Function to create checkout session
      const { data, error } = await supabase.functions.invoke('create-checkout-session', {
        body: {
          certificateType: energieausweisData.certificate_type,
          successUrl: successUrl,
          cancelUrl: cancelUrl,
          paymentId: activeCertificateId,
          userEmail: user.email,
          orderNumber: `EA-${activeCertificateId.slice(-8).toUpperCase()}`
        }
      });

      if (error) {
        throw new Error(error.message || 'Fehler beim Erstellen der Checkout-Session.');
      }

      if (!data?.url) {
        throw new Error('Keine Checkout-URL erhalten.');
      }

      return data;
    },
    onSuccess: async (data: any) => {
      // Update any relevant queries that might be affected by this operation using standardized query keys
      queryClient.invalidateQueries({ queryKey: certificateQueryKeys.detail(activeCertificateId ?? 'none') });

      // Store the checkout session ID in localStorage for potential recovery
      if (data.id) {
        localStorage.setItem('lastCheckoutSessionId', data.id);

        // Track payment attempt
        await trackPaymentAttempt(data.id, data.amount_cents);
      }

      // For Stripe Checkout, we still need to use window.location.href
      // as it's an external redirect to Stripe's hosted page
      window.location.href = data.url;
    },
    onError: (error: CheckoutError) => {
      console.error('Checkout error:', error);

      // Handle specific error codes if available
      if (error.code === 'payment_failed') {
        setError('Die Zahlung konnte nicht verarbeitet werden. Bitte versuchen Sie es erneut.');
      } else if (error.code === 'session_creation_failed') {
        setError('Fehler beim Erstellen der Zahlungssitzung. Bitte versuchen Sie es später erneut.');
      } else {
        setError(error.message || 'Fehler beim Starten der Zahlung.');
      }

      setIsProcessingPayment(false);
    }
  });

  // Mutation to update certificate status to 'payment_initiated' when checkout starts (using stable mutation)
  const updatePaymentInitiatedMutation = useStableMutation({
    mutationFn: async () => {
      if (!activeCertificateId) throw new Error('Kein aktives Zertifikat ausgewählt.');

      // First check current status to ensure unidirectional progression
      const { data: currentData, error: fetchError } = await supabase
        .from('energieausweise')
        .select('status')
        .eq('id', activeCertificateId)
        .single();

      if (fetchError) throw fetchError;

      // Only prevent retry from successful payment - allow retry from failed states
      if (currentData?.status === 'payment_complete') {
        console.log(`Status update prevented: Cannot retry payment after successful completion '${currentData.status}'`);
        return null;
      }

      const { data, error } = await supabase
        .from('energieausweise')
        .update({
          status: 'payment_initiated',
          updated_at: new Date().toISOString(),
        })
        .eq('id', activeCertificateId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      if (data) {
        console.log('Certificate status updated to payment_initiated:', data.id);
        // Update the query cache to reflect the new status using standardized query keys
        queryClient.setQueryData(certificateQueryKeys.detail(activeCertificateId ?? 'none'), data);
        queryClient.invalidateQueries({ queryKey: certificateQueryKeys.detail(activeCertificateId ?? 'none') });

        // Now proceed with creating the checkout session
        createCheckoutMutation.mutate();
      } else {
        console.log('Payment initiation was prevented - certificate already successfully paid');
        setError('Zahlung kann nicht gestartet werden. Zertifikat wurde bereits erfolgreich bezahlt.');
        setIsProcessingPayment(false);
      }
    },
    onError: (error) => {
      console.error('Error updating certificate status to payment_initiated:', error);
      setError('Fehler beim Aktualisieren des Zertifikatsstatus.');
      setIsProcessingPayment(false);
    },
  });

  // Function to save form state before checkout
  const saveFormState = () => {
    if (energieausweisData) {
      // Save the current state to localStorage
      localStorage.setItem('checkoutFormState', JSON.stringify({
        certificateId: activeCertificateId,
        timestamp: new Date().toISOString()
      }));
    }
  };

  // Main checkout handler function
  const handleCheckout = useCallback(async (
    legalConsent: any,
    isAnonymous: boolean,
    onShowAccountConversion: () => void
  ) => {
    console.log('🚀 handleCheckout called', {
      hasData: !!energieausweisData,
      isAnonymous,
      legalConsent,
      certificateId: activeCertificateId
    });

    if (!energieausweisData) {
      console.error('❌ No certificate data available');
      setError('Keine Daten verfügbar. Bitte füllen Sie zuerst alle Formulare aus.');
      return;
    }

    // Check legal consent
    if (!legalConsent.agb || !legalConsent.datenschutz || !legalConsent.widerruf || !legalConsent.dataAccuracy) {
      console.error('❌ Legal consent not complete', legalConsent);
      setError('Bitte stimmen Sie allen rechtlichen Bestimmungen zu, um fortzufahren.');
      return;
    }

    // For anonymous users, show account conversion modal first
    if (isAnonymous) {
      console.log('👤 Anonymous user detected, showing account conversion modal');
      onShowAccountConversion();
      return;
    }

    console.log('✅ Proceeding with payment initiation');

    // Save current form state before proceeding
    saveFormState();

    setIsProcessingPayment(true);
    setError(null);

    // First update status to 'payment_initiated', then create checkout session
    updatePaymentInitiatedMutation.mutate();
  }, [energieausweisData, updatePaymentInitiatedMutation]);

  // Debug logging to track what's changing
  const debugRef = useRef({
    isProcessingPayment,
    error,
    handleCheckout,
    setError,
    setIsProcessingPayment,
    updatePaymentInitiatedMutation,
    createCheckoutMutation,
  });

  const currentValues = {
    isProcessingPayment,
    error,
    handleCheckout,
    setError,
    setIsProcessingPayment,
    updatePaymentInitiatedMutation,
    createCheckoutMutation,
  };

  // Check what changed
  Object.entries(currentValues).forEach(([key, value]) => {
    if (debugRef.current[key as keyof typeof debugRef.current] !== value) {
      console.log(`🔍 usePaymentFlow dependency changed: ${key}`, {
        previous: debugRef.current[key as keyof typeof debugRef.current],
        current: value,
        isFunction: typeof value === 'function',
        isMutation: key.includes('Mutation'),
        mutationStatus: key.includes('Mutation') ? {
          isPending: (value as any)?.isPending,
          isSuccess: (value as any)?.isSuccess,
          isError: (value as any)?.isError,
        } : undefined
      });
    }
  });

  debugRef.current = currentValues;

  // Memoize the return object to prevent new references on every render
  return useMemo(() => ({
    // State
    isProcessingPayment,
    error,

    // Actions
    handleCheckout,
    setError,
    setIsProcessingPayment,

    // Mutations (for external access if needed)
    updatePaymentInitiatedMutation,
    createCheckoutMutation,
  }), [
    isProcessingPayment,
    error,
    handleCheckout,
    setError,
    setIsProcessingPayment,
    updatePaymentInitiatedMutation,
    createCheckoutMutation
  ]);
};