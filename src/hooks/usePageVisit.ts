import { useEffect, useMemo, useRef } from 'react';
import { useNavigationState, type PageType } from './useNavigationState';
import { useCertificateType } from './useCertificateType';

/**
 * Hook to automatically mark a page as visited when the component mounts
 * This should be used in each form page component to track user navigation
 *
 * PERFORMANCE OPTIMIZATIONS:
 * - Debounced page visit marking to prevent rapid database updates during route transitions
 * - Prevents duplicate calls for the same page within a short time window
 * - Memoized certificate type to prevent unnecessary effect triggers
 */
export const usePageVisit = (pageType: PageType) => {
  const { certificateType } = useCertificateType();
  const { markPageAsVisited } = useNavigationState(certificateType);

  // Track the last page visit to prevent duplicate calls
  const lastPageVisitRef = useRef<{ pageType: PageType; timestamp: number } | null>(null);

  // Memoize the certificate type to prevent unnecessary effect triggers
  const memoizedCertificateType = useMemo(() => certificateType, [certificateType]);

  useEffect(() => {
    // Only mark the page as visited if certificate type is available
    if (!memoizedCertificateType) return;

    // Prevent duplicate calls within a short time window (1 second)
    const now = Date.now();
    if (lastPageVisitRef.current &&
        lastPageVisitRef.current.pageType === pageType &&
        now - lastPageVisitRef.current.timestamp < 1000) {
      return;
    }

    // Debounce the page visit marking to prevent rapid database updates during route transitions
    const timeoutId = setTimeout(() => {
      // Update the last page visit reference
      lastPageVisitRef.current = { pageType, timestamp: now };

      // markPageAsVisited is now async, but we don't need to await it here
      // as it's just updating the status in the background
      // PHANTOM RENDER FIX: Wrap in requestIdleCallback to prevent render blocking
      if (typeof requestIdleCallback !== 'undefined') {
        requestIdleCallback(() => markPageAsVisited(pageType));
      } else {
        // Fallback for environments without requestIdleCallback
        setTimeout(() => markPageAsVisited(pageType), 0);
      }
    }, 50); // PHANTOM RENDER FIX: Reduced from 100ms to 50ms to minimize phantom render window

    return () => clearTimeout(timeoutId);

    // Note: markPageAsVisited is intentionally excluded from dependencies
    // as it's a stable callback and including it causes unnecessary re-executions
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageType, memoizedCertificateType]);
};
