import { useMemo, useCallback, useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { z } from 'zod';
import { supabase } from '../lib/supabase';
import { useCertificate } from '../contexts/CertificateContext';
import { useNavigationState } from './useNavigationState';
import { certificateQueryKeys } from '../utils/queryKeys';
import {
  energietraegerOptions,
  getEnergietraegerLabel,
  getEnergySourceUnit,
  type EnergyUnit
} from '../utils/energyCarrierUtils';

// Certificate types
export type CertificateType = 'WG/V' | 'WG/B' | 'NWG/V';

// Custom Zod refinement for future date validation
const futureDateValidation = z.string().optional().refine(
  (date) => {
    if (!date) return true;
    const inputDate = new Date(date);
    const today = new Date();
    today.setHours(23, 59, 59, 999);
    return inputDate <= today;
  },
  {
    message: 'Das Datum darf nicht in der Zukunft liegen'
  }
);

// Base schema for ETr1 (always required)
const baseEtr1Schema = z.object({
  ETr1_Kategorie: z.string().optional(),
  ETr1_Heizung: z.enum(['0', '1']).default('1'),
  ETr1_TWW: z.enum(['0', '1']).default('1'),
  ETr1_ZusatzHz: z.enum(['0', '1']).default('0'),
  ETr1_Lueften: z.enum(['0', '1']).default('0'),
  ETr1_Licht: z.enum(['0', '1']).default('0'),
  ETr1_Kuehlen: z.enum(['0', '1']).default('0'),
  ETr1_Sonst: z.enum(['0', '1']).default('0'),
  ETr1_PrimFaktor: z.string().default('1'),
  ETr1_Anteil_erneuerbar: z.string().optional(),
  ETr1_Anteil_KWK: z.string().optional(),
  ETr1_isFw: z.enum(['0', '1']).default('0'),
  ETr1_gebaeudeNahErzeugt: z.enum(['0', '1']).default('0'),
  ETr1_Name: z.string().optional(),
  // Jahr 1-3 fields with date validation
  ETr1_Jahr1_von: futureDateValidation,
  ETr1_Jahr1_bis: futureDateValidation,
  ETr1_Jahr1_Menge: z.string().min(1, 'Verbrauchsmenge ist erforderlich'),
  ETr1_Jahr1_Leerstand: z.string().optional(),
  ETr1_Jahr1_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
  ETr1_Jahr1_Leerstand_von: futureDateValidation,
  ETr1_Jahr1_Leerstand_bis: futureDateValidation,
  ETr1_Jahr2_von: futureDateValidation,
  ETr1_Jahr2_bis: futureDateValidation,
  ETr1_Jahr2_Menge: z.string().min(1, 'Verbrauchsmenge ist erforderlich'),
  ETr1_Jahr2_Leerstand: z.string().optional(),
  ETr1_Jahr2_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
  ETr1_Jahr2_Leerstand_von: futureDateValidation,
  ETr1_Jahr2_Leerstand_bis: futureDateValidation,
  ETr1_Jahr3_von: futureDateValidation,
  ETr1_Jahr3_bis: futureDateValidation,
  ETr1_Jahr3_Menge: z.string().min(1, 'Verbrauchsmenge ist erforderlich'),
  ETr1_Jahr3_Leerstand: z.string().optional(),
  ETr1_Jahr3_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
  ETr1_Jahr3_Leerstand_von: futureDateValidation,
  ETr1_Jahr3_Leerstand_bis: futureDateValidation,
});

// Schema for ETr2 (conditional)
const etr2Schema = z.object({
  ETr2_Kategorie: z.string().optional(),
  ETr2_Heizung: z.enum(['0', '1']).default('0'),
  ETr2_TWW: z.enum(['0', '1']).default('0'),
  ETr2_ZusatzHz: z.enum(['0', '1']).default('0'),
  ETr2_Lueften: z.enum(['0', '1']).default('0'),
  ETr2_Licht: z.enum(['0', '1']).default('0'),
  ETr2_Kuehlen: z.enum(['0', '1']).default('0'),
  ETr2_Sonst: z.enum(['0', '1']).default('0'),
  ETr2_PrimFaktor: z.string().default('1'),
  ETr2_Anteil_erneuerbar: z.string().optional(),
  ETr2_Anteil_KWK: z.string().optional(),
  ETr2_isFw: z.enum(['0', '1']).default('0'),
  ETr2_gebaeudeNahErzeugt: z.enum(['0', '1']).default('0'),
  ETr2_Name: z.string().optional(),
  // Jahr 1-3 fields for ETr2
  ETr2_Jahr1_von: futureDateValidation,
  ETr2_Jahr1_bis: futureDateValidation,
  ETr2_Jahr1_Menge: z.string().min(1, 'Verbrauchsmenge ist erforderlich'),
  ETr2_Jahr1_Leerstand: z.string().optional(),
  ETr2_Jahr1_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
  ETr2_Jahr1_Leerstand_von: futureDateValidation,
  ETr2_Jahr1_Leerstand_bis: futureDateValidation,
  ETr2_Jahr2_von: futureDateValidation,
  ETr2_Jahr2_bis: futureDateValidation,
  ETr2_Jahr2_Menge: z.string().min(1, 'Verbrauchsmenge ist erforderlich'),
  ETr2_Jahr2_Leerstand: z.string().optional(),
  ETr2_Jahr2_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
  ETr2_Jahr2_Leerstand_von: futureDateValidation,
  ETr2_Jahr2_Leerstand_bis: futureDateValidation,
  ETr2_Jahr3_von: futureDateValidation,
  ETr2_Jahr3_bis: futureDateValidation,
  ETr2_Jahr3_Menge: z.string().min(1, 'Verbrauchsmenge ist erforderlich'),
  ETr2_Jahr3_Leerstand: z.string().optional(),
  ETr2_Jahr3_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
  ETr2_Jahr3_Leerstand_von: futureDateValidation,
  ETr2_Jahr3_Leerstand_bis: futureDateValidation,
});

// Schema for ETr3 (conditional)
const etr3Schema = z.object({
  ETr3_Kategorie: z.string().optional(),
  ETr3_Heizung: z.enum(['0', '1']).default('0'),
  ETr3_TWW: z.enum(['0', '1']).default('0'),
  ETr3_ZusatzHz: z.enum(['0', '1']).default('0'),
  ETr3_Lueften: z.enum(['0', '1']).default('0'),
  ETr3_Licht: z.enum(['0', '1']).default('0'),
  ETr3_Kuehlen: z.enum(['0', '1']).default('0'),
  ETr3_Sonst: z.enum(['0', '1']).default('0'),
  ETr3_PrimFaktor: z.string().default('1'),
  ETr3_Anteil_erneuerbar: z.string().optional(),
  ETr3_Anteil_KWK: z.string().optional(),
  ETr3_isFw: z.enum(['0', '1']).default('0'),
  ETr3_gebaeudeNahErzeugt: z.enum(['0', '1']).default('0'),
  ETr3_Name: z.string().optional(),
  // Jahr 1-3 fields for ETr3
  ETr3_Jahr1_von: futureDateValidation,
  ETr3_Jahr1_bis: futureDateValidation,
  ETr3_Jahr1_Menge: z.string().min(1, 'Verbrauchsmenge ist erforderlich'),
  ETr3_Jahr1_Leerstand: z.string().optional(),
  ETr3_Jahr1_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
  ETr3_Jahr1_Leerstand_von: futureDateValidation,
  ETr3_Jahr1_Leerstand_bis: futureDateValidation,
  ETr3_Jahr2_von: futureDateValidation,
  ETr3_Jahr2_bis: futureDateValidation,
  ETr3_Jahr2_Menge: z.string().min(1, 'Verbrauchsmenge ist erforderlich'),
  ETr3_Jahr2_Leerstand: z.string().optional(),
  ETr3_Jahr2_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
  ETr3_Jahr2_Leerstand_von: futureDateValidation,
  ETr3_Jahr2_Leerstand_bis: futureDateValidation,
  ETr3_Jahr3_von: futureDateValidation,
  ETr3_Jahr3_bis: futureDateValidation,
  ETr3_Jahr3_Menge: z.string().min(1, 'Verbrauchsmenge ist erforderlich'),
  ETr3_Jahr3_Leerstand: z.string().optional(),
  ETr3_Jahr3_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
  ETr3_Jahr3_Leerstand_von: futureDateValidation,
  ETr3_Jahr3_Leerstand_bis: futureDateValidation,
});

// Complete schema for TypeScript type inference
const verbrauchSchema = baseEtr1Schema.merge(etr2Schema).merge(etr3Schema);
export type VerbrauchFormValues = z.infer<typeof verbrauchSchema>;

// Function to create dynamic validation schema based on visible sections
export const createDynamicVerbrauchSchema = (showEtr2: boolean, showEtr3: boolean) => {
  let schema = baseEtr1Schema;
  
  if (showEtr2) {
    schema = schema.merge(etr2Schema);
  }
  
  if (showEtr3) {
    schema = schema.merge(etr3Schema);
  }
  
  return schema;
};

// Hook return interface
export interface UseVerbrauchDataReturn {
  // Data
  existingData: any;
  certificateType: CertificateType;
  initialValues: Partial<VerbrauchFormValues>;

  // Loading and error states
  isLoading: boolean;
  isError: boolean;
  error: Error | null;

  // Mutations
  saveMutation: any;
  
  // Utility functions
  getEnergyUnit: (energietraegerValue: string) => EnergyUnit;
  handleKategorieChange: (kategorieValue: string, nameFieldName: keyof VerbrauchFormValues, form: any) => void;
  validateNotFutureDate: (dateString: string) => boolean;
  hasClientSideValidationErrors: (formValues: Partial<VerbrauchFormValues>) => boolean;
  
  // Schema utilities
  createDynamicSchema: (showEtr2: boolean, showEtr3: boolean) => z.ZodSchema;
  
  // Constants
  energietraegerOptions: typeof energietraegerOptions;
}

/**
 * Custom hook for managing Verbrauch (consumption) data
 * Handles data fetching, state management, and business logic for consumption forms
 */
export const useVerbrauchData = (): UseVerbrauchDataReturn => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { activeCertificateId } = useCertificate();
  
  // Memoize certificate ID to prevent unnecessary re-renders
  const memoizedCertificateId = useMemo(() => activeCertificateId, [activeCertificateId]);
  
  // State management
  const [certificateType, setCertificateType] = useState<CertificateType>('WG/V');
  
  const { markPageAsVisited } = useNavigationState(certificateType);
  
  // Fetch certificate type
  const { data: certificateData } = useQuery({
    queryKey: ['energieausweise', 'certificate_type', memoizedCertificateId],
    queryFn: async () => {
      if (!memoizedCertificateId) return null;
      
      const { data, error } = await supabase
        .from('energieausweise')
        .select('certificate_type')
        .eq('id', memoizedCertificateId)
        .single();
      
      if (error) throw error;
      return data;
    },
    enabled: !!memoizedCertificateId,
    retry: 1,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: false,
    refetchInterval: false,
    refetchIntervalInBackground: false,
    retryDelay: 1000,
  });
  
  // Update certificate type when data is fetched
  useEffect(() => {
    if (certificateData?.certificate_type) {
      setCertificateType(certificateData.certificate_type as CertificateType);
    }
  }, [certificateData]);
  
  // Fetch existing verbrauch data
  const { data: existingData, isError, error } = useQuery({
    queryKey: certificateQueryKeys.detail(memoizedCertificateId ?? 'none'),
    queryFn: async () => {
      if (!memoizedCertificateId) return null;
      
      const { data, error } = await supabase
        .from('energieausweise')
        .select('verbrauchsdaten')
        .eq('id', memoizedCertificateId)
        .single();
      
      if (error) throw error;
      return data;
    },
    enabled: !!memoizedCertificateId,
    retry: 1,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: false,
    refetchInterval: false,
    refetchIntervalInBackground: false,
    retryDelay: 1000,
  });



  // Initial form values
  const initialValues: Partial<VerbrauchFormValues> = useMemo(() => ({
    ETr1_Kategorie: 'BK_GAS',
    ETr1_Heizung: '1',
    ETr1_TWW: '1',
    ETr1_ZusatzHz: '0',
    ETr1_Lueften: '0',
    ETr1_Licht: '0',
    ETr1_Kuehlen: '0',
    ETr1_Sonst: '0',
    ETr1_PrimFaktor: '1',
    ETr1_Anteil_erneuerbar: '',
    ETr1_Anteil_KWK: '',
    ETr1_isFw: '0',
    ETr1_gebaeudeNahErzeugt: '0',
    ETr1_Name: '',
    ETr1_Jahr1_von: '',
    ETr1_Jahr1_bis: '',
    ETr1_Jahr1_Menge: '',
    ETr1_Jahr1_Leerstand: '0',
    ETr1_Jahr1_Leerstand_hasLeerstand: '0',
    ETr1_Jahr1_Leerstand_von: '',
    ETr1_Jahr1_Leerstand_bis: '',
    ETr1_Jahr2_von: '',
    ETr1_Jahr2_bis: '',
    ETr1_Jahr2_Menge: '',
    ETr1_Jahr2_Leerstand: '0',
    ETr1_Jahr2_Leerstand_hasLeerstand: '0',
    ETr1_Jahr2_Leerstand_von: '',
    ETr1_Jahr2_Leerstand_bis: '',
    ETr1_Jahr3_von: '',
    ETr1_Jahr3_bis: '',
    ETr1_Jahr3_Menge: '',
    ETr1_Jahr3_Leerstand: '0',
    ETr1_Jahr3_Leerstand_hasLeerstand: '0',
    ETr1_Jahr3_Leerstand_von: '',
    ETr1_Jahr3_Leerstand_bis: '',
    // ETr2 defaults
    ETr2_Kategorie: '',
    ETr2_Heizung: '0',
    ETr2_TWW: '0',
    ETr2_ZusatzHz: '0',
    ETr2_Lueften: '0',
    ETr2_Licht: '0',
    ETr2_Kuehlen: '0',
    ETr2_Sonst: '0',
    ETr2_PrimFaktor: '1',
    ETr2_Anteil_erneuerbar: '',
    ETr2_Anteil_KWK: '',
    ETr2_isFw: '0',
    ETr2_gebaeudeNahErzeugt: '0',
    ETr2_Name: '',
    // ETr3 defaults
    ETr3_Kategorie: '',
    ETr3_Heizung: '0',
    ETr3_TWW: '0',
    ETr3_ZusatzHz: '0',
    ETr3_Lueften: '0',
    ETr3_Licht: '0',
    ETr3_Kuehlen: '0',
    ETr3_Sonst: '0',
    ETr3_PrimFaktor: '1',
    ETr3_Anteil_erneuerbar: '',
    ETr3_Anteil_KWK: '',
    ETr3_isFw: '0',
    ETr3_gebaeudeNahErzeugt: '0',
    ETr3_Name: '',
  }), []);

  // Save mutation
  const saveMutation = useMutation({
    mutationFn: async (data: VerbrauchFormValues) => {
      if (!memoizedCertificateId) throw new Error('Kein aktives Zertifikat ausgewählt.');

      const { data: result, error } = await supabase
        .from('energieausweise')
        .update({
          verbrauchsdaten: data,
          updated_at: new Date().toISOString(),
        })
        .eq('id', memoizedCertificateId)
        .select();

      if (error) throw error;
      return result;
    },
    onSuccess: async () => {
      // CRITICAL FIX: Invalidate queries to mark data as stale
      // This ensures ZusammenfassungPage will refetch fresh data on mount
      await queryClient.invalidateQueries({
        queryKey: certificateQueryKeys.detail(memoizedCertificateId ?? 'none'),
        refetchType: 'none' // Don't refetch immediately, let the summary page handle it
      });

      // Also invalidate the legacy query key for backwards compatibility
      await queryClient.invalidateQueries({
        queryKey: ['energieausweise', memoizedCertificateId],
        refetchType: 'none'
      });

      // Update navigation state
      await markPageAsVisited('zusammenfassung');

      // Navigate to next page
      navigate({ to: '/erfassen/zusammenfassung' });
    },
    onError: (error) => {
      console.error('Error saving verbrauch data:', error);
    },
  });

  // Utility functions
  const getEnergyUnit = useCallback((energietraegerValue: string): EnergyUnit => {
    return getEnergySourceUnit(energietraegerValue);
  }, []);

  const handleKategorieChange = useCallback((
    kategorieValue: string,
    nameFieldName: keyof VerbrauchFormValues,
    form: any
  ) => {
    const label = getEnergietraegerLabel(kategorieValue);
    form.setFieldValue(nameFieldName, label);
  }, []);

  const validateNotFutureDate = useCallback((dateString: string): boolean => {
    if (!dateString) return true;
    const inputDate = new Date(dateString);
    const today = new Date();
    today.setHours(23, 59, 59, 999);
    return inputDate <= today;
  }, []);

  const hasClientSideValidationErrors = useCallback((formValues: Partial<VerbrauchFormValues>): boolean => {
    const dateFields = [
      'ETr1_Jahr1_von', 'ETr1_Jahr1_bis', 'ETr1_Jahr2_von', 'ETr1_Jahr2_bis', 'ETr1_Jahr3_von', 'ETr1_Jahr3_bis',
      'ETr1_Jahr1_Leerstand_von', 'ETr1_Jahr1_Leerstand_bis', 'ETr1_Jahr2_Leerstand_von', 'ETr1_Jahr2_Leerstand_bis',
      'ETr1_Jahr3_Leerstand_von', 'ETr1_Jahr3_Leerstand_bis',
      'ETr2_Jahr1_von', 'ETr2_Jahr1_bis', 'ETr2_Jahr2_von', 'ETr2_Jahr2_bis', 'ETr2_Jahr3_von', 'ETr2_Jahr3_bis',
      'ETr2_Jahr1_Leerstand_von', 'ETr2_Jahr1_Leerstand_bis', 'ETr2_Jahr2_Leerstand_von', 'ETr2_Jahr2_Leerstand_bis',
      'ETr2_Jahr3_Leerstand_von', 'ETr2_Jahr3_Leerstand_bis',
      'ETr3_Jahr1_von', 'ETr3_Jahr1_bis', 'ETr3_Jahr2_von', 'ETr3_Jahr2_bis', 'ETr3_Jahr3_von', 'ETr3_Jahr3_bis',
      'ETr3_Jahr1_Leerstand_von', 'ETr3_Jahr1_Leerstand_bis', 'ETr3_Jahr2_Leerstand_von', 'ETr3_Jahr2_Leerstand_bis',
      'ETr3_Jahr3_Leerstand_von', 'ETr3_Jahr3_Leerstand_bis'
    ];

    return dateFields.some(fieldName => {
      const value = formValues[fieldName as keyof VerbrauchFormValues] as string;
      return value && !validateNotFutureDate(value);
    });
  }, [validateNotFutureDate]);

  const createDynamicSchema = useCallback((showEtr2: boolean, showEtr3: boolean) => {
    return createDynamicVerbrauchSchema(showEtr2, showEtr3);
  }, []);

  // Loading state - true if either query is loading
  const isLoading = useMemo(() => {
    return !existingData && !isError;
  }, [existingData, isError]);

  // Memoize the return object to prevent unnecessary re-renders
  return useMemo(() => ({
    // Data
    existingData,
    certificateType,
    initialValues,

    // Loading and error states
    isLoading,
    isError,
    error,

    // Mutations
    saveMutation,

    // Utility functions
    getEnergyUnit,
    handleKategorieChange,
    validateNotFutureDate,
    hasClientSideValidationErrors,

    // Schema utilities
    createDynamicSchema,

    // Constants
    energietraegerOptions,
  }), [
    existingData,
    certificateType,
    initialValues,
    isLoading,
    isError,
    error,
    saveMutation,
    getEnergyUnit,
    handleKategorieChange,
    validateNotFutureDate,
    hasClientSideValidationErrors,
    createDynamicSchema,
  ]);
};
