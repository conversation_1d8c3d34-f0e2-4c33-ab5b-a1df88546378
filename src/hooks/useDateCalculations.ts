import { useCallback } from 'react';

/**
 * Interface for calculated date ranges for consumption periods
 */
export interface CalculatedDateRanges {
  jahr1Von: string;
  jahr1Bis: string;
  jahr2Von: string;
  jahr2Bis: string;
  jahr3Von: string;
  jahr3Bis: string;
}

/**
 * Interface for the date calculations hook return value
 */
export interface DateCalculationsReturn {
  calculateDateWithYearOffset: (dateString: string, yearOffset: number, dayOffset?: number) => string;
  calculateAllDatesFromEndDate: (endDate: string) => CalculatedDateRanges;
  updateCalculatedDates: (endDate: string, prefix: 'ETr1' | 'ETr2' | 'ETr3', form: any) => void;
}

/**
 * Custom hook for date calculations in consumption forms
 * Provides utilities for calculating consumption period date ranges
 */
export const useDateCalculations = (): DateCalculationsReturn => {
  /**
   * Calculate a date with year and day offset
   * @param dateString - The base date string in YYYY-MM-DD format
   * @param yearOffset - Number of years to add/subtract
   * @param dayOffset - Number of days to add/subtract (default: 0)
   * @returns Calculated date string in YYYY-MM-DD format
   */
  const calculateDateWithYearOffset = useCallback((
    dateString: string, 
    yearOffset: number, 
    dayOffset: number = 0
  ): string => {
    if (!dateString) return '';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';

    // Add or subtract the specified number of years
    date.setFullYear(date.getFullYear() + yearOffset);

    // Add or subtract the specified number of days
    if (dayOffset !== 0) {
      date.setDate(date.getDate() + dayOffset);
    }

    // Return in YYYY-MM-DD format for date inputs
    return date.toISOString().split('T')[0];
  }, []);

  /**
   * Calculate all date ranges from an end date
   * Creates three consecutive 12-month periods going backwards from the end date
   * @param endDate - The end date of the most recent consumption period
   * @returns Object containing all calculated date ranges
   */
  const calculateAllDatesFromEndDate = useCallback((endDate: string): CalculatedDateRanges => {
    if (!endDate) {
      return {
        jahr1Von: '',
        jahr1Bis: '',
        jahr2Von: '',
        jahr2Bis: '',
        jahr3Von: '',
        jahr3Bis: ''
      };
    }

    // The input date is the END of the most recent consumption period (Jahr 1)
    // Calculate three consecutive 12-month periods going backwards
    const jahr1Bis = endDate; // Most recent period ends on the input date
    const jahr1Von = calculateDateWithYearOffset(endDate, -1, 1); // Start 1 year before, +1 day
    const jahr2Bis = calculateDateWithYearOffset(endDate, -1); // Second period ends 1 year before
    const jahr2Von = calculateDateWithYearOffset(endDate, -2, 1); // Start 2 years before, +1 day
    const jahr3Bis = calculateDateWithYearOffset(endDate, -2); // Third period ends 2 years before
    const jahr3Von = calculateDateWithYearOffset(endDate, -3, 1); // Start 3 years before, +1 day

    return {
      jahr1Von,
      jahr1Bis,
      jahr2Von,
      jahr2Bis,
      jahr3Von,
      jahr3Bis
    };
  }, [calculateDateWithYearOffset]);

  /**
   * Update calculated dates in the form for a given energy carrier
   * @param endDate - The end date to calculate from
   * @param prefix - The energy carrier prefix (ETr1, ETr2, ETr3)
   * @param form - The form instance to update
   */
  const updateCalculatedDates = useCallback((
    endDate: string, 
    prefix: 'ETr1' | 'ETr2' | 'ETr3', 
    form: any
  ) => {
    if (!endDate) return;

    const calculatedDates = calculateAllDatesFromEndDate(endDate);

    // Update all calculated dates - now including Jahr1_von since we calculate backwards
    form.setFieldValue(`${prefix}_Jahr1_von`, calculatedDates.jahr1Von);
    form.setFieldValue(`${prefix}_Jahr1_bis`, calculatedDates.jahr1Bis);
    form.setFieldValue(`${prefix}_Jahr2_von`, calculatedDates.jahr2Von);
    form.setFieldValue(`${prefix}_Jahr2_bis`, calculatedDates.jahr2Bis);
    form.setFieldValue(`${prefix}_Jahr3_von`, calculatedDates.jahr3Von);
    form.setFieldValue(`${prefix}_Jahr3_bis`, calculatedDates.jahr3Bis);
  }, [calculateAllDatesFromEndDate]);

  return {
    calculateDateWithYearOffset,
    calculateAllDatesFromEndDate,
    updateCalculatedDates
  };
};
