import { useState, useEffect, useMemo } from 'react';
import type { VerbrauchFormValues } from './useVerbrauchData';

// Energy carrier state interface
export interface EnergyCarrierState {
  showEtr2: boolean;
  showEtr3: boolean;
  etr1IsFw: boolean;
  etr2IsFw: boolean;
  etr3IsFw: boolean;
}

// Fernwärme states interface for easier access
export interface FernwaermeStates {
  etr1IsFw: boolean;
  etr2IsFw: boolean;
  etr3IsFw: boolean;
}

// Hook return interface
export interface UseEnergyCarrierStateReturn {
  showEtr2: boolean;
  setShowEtr2: (show: boolean) => void;
  showEtr3: boolean;
  setShowEtr3: (show: boolean) => void;
  fernwaermeStates: FernwaermeStates;
  setEtr1IsFw: (isFw: boolean) => void;
  setEtr2IsFw: (isFw: boolean) => void;
  setEtr3IsFw: (isFw: boolean) => void;
  energyCarrierState: EnergyCarrierState;
  setEnergyCarrierState: React.Dispatch<React.SetStateAction<EnergyCarrierState>>;
}

/**
 * Custom hook for managing energy carrier visibility states and Fernwärme-related state logic
 * 
 * @param existingData - Current form data containing existing energy carrier information
 * @returns Object with energy carrier state management functions and current states
 */
export const useEnergyCarrierState = (existingData?: any): UseEnergyCarrierStateReturn => {
  // Main energy carrier state
  const [energyCarrierState, setEnergyCarrierState] = useState<EnergyCarrierState>({
    showEtr2: false,
    showEtr3: false,
    etr1IsFw: false,
    etr2IsFw: false,
    etr3IsFw: false,
  });

  // Update energy carrier state when existing data is loaded
  useEffect(() => {
    if (existingData && existingData.verbrauchsdaten) {
      const verbrauchsdaten = existingData.verbrauchsdaten as Partial<VerbrauchFormValues>;

      setEnergyCarrierState(prev => ({
        ...prev,
        showEtr2: !!verbrauchsdaten.ETr2_Kategorie,
        showEtr3: !!verbrauchsdaten.ETr3_Kategorie,
        etr1IsFw: verbrauchsdaten.ETr1_isFw === '1',
        etr2IsFw: verbrauchsdaten.ETr2_isFw === '1',
        etr3IsFw: verbrauchsdaten.ETr3_isFw === '1',
      }));
    }
  }, [existingData]);

  // Memoized fernwärme states for easier access
  const fernwaermeStates = useMemo<FernwaermeStates>(() => ({
    etr1IsFw: energyCarrierState.etr1IsFw,
    etr2IsFw: energyCarrierState.etr2IsFw,
    etr3IsFw: energyCarrierState.etr3IsFw,
  }), [energyCarrierState.etr1IsFw, energyCarrierState.etr2IsFw, energyCarrierState.etr3IsFw]);

  // Individual setter functions for easier component integration
  const setShowEtr2 = useMemo(() => (show: boolean) => {
    setEnergyCarrierState(prev => ({ ...prev, showEtr2: show }));
  }, []);

  const setShowEtr3 = useMemo(() => (show: boolean) => {
    setEnergyCarrierState(prev => ({ ...prev, showEtr3: show }));
  }, []);

  const setEtr1IsFw = useMemo(() => (isFw: boolean) => {
    setEnergyCarrierState(prev => ({ ...prev, etr1IsFw: isFw }));
  }, []);

  const setEtr2IsFw = useMemo(() => (isFw: boolean) => {
    setEnergyCarrierState(prev => ({ ...prev, etr2IsFw: isFw }));
  }, []);

  const setEtr3IsFw = useMemo(() => (isFw: boolean) => {
    setEnergyCarrierState(prev => ({ ...prev, etr3IsFw: isFw }));
  }, []);

  // Memoize the return object to prevent unnecessary re-renders
  return useMemo(() => ({
    showEtr2: energyCarrierState.showEtr2,
    setShowEtr2,
    showEtr3: energyCarrierState.showEtr3,
    setShowEtr3,
    fernwaermeStates,
    setEtr1IsFw,
    setEtr2IsFw,
    setEtr3IsFw,
    energyCarrierState,
    setEnergyCarrierState,
  }), [
    energyCarrierState,
    setShowEtr2,
    setShowEtr3,
    fernwaermeStates,
    setEtr1IsFw,
    setEtr2IsFw,
    setEtr3IsFw,
  ]);
};
