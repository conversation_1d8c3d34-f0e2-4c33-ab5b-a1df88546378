import { useState, useEffect, useMemo } from 'react';
import { useForm, useField } from '@tanstack/react-form';
import { z } from 'zod';
import { Link } from '@tanstack/react-router';
import { useCertificate } from '../../contexts/CertificateContext';
import { CheckboxField } from '../../components/ui/CheckboxField';

import { DirectoryBasedFileUpload } from '../../components/ui/DirectoryBasedFileUpload';
import { ValidationErrorSummary } from '../../components/ui/ValidationErrorSummary';
import { Breadcrumb } from '../../components/ui/Breadcrumb';
import { usePageVisit } from '../../hooks/usePageVisit';

// Import new utility hooks and functions
import { useDateCalculations } from '../../hooks/useDateCalculations';
import { useVerbrauchData, type VerbrauchFormValues } from '../../hooks/useVerbrauchData';
import { useEnergyCarrierState } from '../../hooks/useEnergyCarrierState';

// Import new enhanced field components
import {
  EnhancedConsumptionAmountField,
  EnhancedSelectFieldWithCallback,
  VacancyPeriodField
} from '../../components/verbrauch';

// This component is now imported from '../../components/ui/FileWithSignedUrl'
// and the local implementation has been removed to avoid naming conflicts







export const VerbrauchPage = () => {
  // Get certificate context
  const { activeCertificateId } = useCertificate();

  // Memoize certificateId to prevent unnecessary re-renders of child components
  const memoizedCertificateId = useMemo(() => activeCertificateId, [activeCertificateId]);

  // Use the new useVerbrauchData hook for all data management
  const {
    existingData,
    certificateType,
    initialValues,
    isLoading,
    isError,
    error,
    saveMutation,
    handleKategorieChange,
    validateNotFutureDate,
    hasClientSideValidationErrors,
    createDynamicSchema,
    energietraegerOptions,
  } = useVerbrauchData();

  // Use the new useEnergyCarrierState hook for energy carrier state management
  const {
    showEtr2,
    setShowEtr2,
    showEtr3,
    setShowEtr3,
    fernwaermeStates,
    setEtr1IsFw,
    setEtr2IsFw,
    setEtr3IsFw,
  } = useEnergyCarrierState(existingData);

  // Local state for form validation and errors
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string[]>>({});

  // Mark this page as visited for navigation tracking
  usePageVisit('verbrauch');

  // Initialize utility hooks
  const { updateCalculatedDates } = useDateCalculations();

  console.log('🏠 VerbrauchPage called');

  // Extract fernwärme states for easier access
  const { etr1IsFw, etr2IsFw, etr3IsFw } = fernwaermeStates;



  // Create the form with initial values from the hook
  const form = useForm({
    defaultValues: initialValues,
    onSubmit: async ({ value }) => {
      setSubmitError(null);
      setValidationErrors({});

      // Check for client-side validation errors first
      if (hasClientSideValidationErrors(value)) {
        setSubmitError('Bitte korrigieren Sie die markierten Felder. Datumswerte dürfen nicht in der Zukunft liegen.');
        return;
      }

      try {
        // Create dynamic validation schema based on visible sections
        const dynamicSchema = createDynamicSchema(showEtr2, showEtr3);

        // Filter form values to only include fields that should be validated
        const filteredFormValues = { ...value };

        // Remove ETr2 fields if not visible
        if (!showEtr2) {
          Object.keys(filteredFormValues).forEach(key => {
            if (key.startsWith('ETr2_')) {
              delete (filteredFormValues as Record<string, any>)[key];
            }
          });
        }

        // Remove ETr3 fields if not visible
        if (!showEtr3) {
          Object.keys(filteredFormValues).forEach(key => {
            if (key.startsWith('ETr3_')) {
              delete (filteredFormValues as Record<string, any>)[key];
            }
          });
        }

        // Validate only the filtered form data using the dynamic schema
        const validatedData = dynamicSchema.parse(filteredFormValues);

        // File uploads are now handled by DirectoryBasedFileUpload component

        // Apply certificate type specific filtering
        const finalFilteredValue = { ...validatedData };

        // If certificate type is not NWG/V, remove the conditional fields
        if (certificateType !== 'NWG/V') {
          // Remove fields that should only be shown for NWG/V
          ['ETr1_ZusatzHz', 'ETr1_Lueften', 'ETr1_Licht', 'ETr1_Kuehlen', 'ETr1_Sonst'].forEach(key => {
            delete (finalFilteredValue as Record<string, any>)[key];
          });

          // Also remove these fields from ETr2 and ETr3 if they exist
          if (showEtr2) {
            ['ETr2_ZusatzHz', 'ETr2_Lueften', 'ETr2_Licht', 'ETr2_Kuehlen', 'ETr2_Sonst'].forEach(key => {
              delete (finalFilteredValue as Record<string, any>)[key];
            });
          }

          if (showEtr3) {
            ['ETr3_ZusatzHz', 'ETr3_Lueften', 'ETr3_Licht', 'ETr3_Kuehlen', 'ETr3_Sonst'].forEach(key => {
              delete (finalFilteredValue as Record<string, any>)[key];
            });
          }
        }

        saveMutation.mutate(finalFilteredValue as VerbrauchFormValues);
      } catch (error) {
        if (error instanceof z.ZodError) {
          // Handle validation errors
          const fieldErrors: Record<string, string[]> = {};

          error.errors.forEach((err) => {
            const fieldName = err.path.join('.');
            if (!fieldErrors[fieldName]) {
              fieldErrors[fieldName] = [];
            }
            fieldErrors[fieldName].push(err.message);
          });

          // Set field errors on the form
          Object.entries(fieldErrors).forEach(([fieldName, errors]) => {
            form.setFieldMeta(fieldName as keyof VerbrauchFormValues, (prev) => ({
              ...prev,
              errors: errors,
            }));
          });

          // Update validation errors state for the summary component
          setValidationErrors(fieldErrors);
          setSubmitError('Bitte korrigieren Sie die markierten Felder.');
        } else {
          setSubmitError('Ein unerwarteter Fehler ist aufgetreten.');
          setValidationErrors({});
        }
      }
    },
  });

  // Update form values when data is loaded - use setFieldValue instead of form.reset to avoid re-rendering
  useEffect(() => {
    if (existingData && existingData.verbrauchsdaten) {
      const verbrauchsdaten = existingData.verbrauchsdaten as Partial<VerbrauchFormValues>;

      // Set individual field values instead of resetting the entire form
      Object.entries(verbrauchsdaten).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          form.setFieldValue(key as keyof VerbrauchFormValues, value);
        }
      });

      // Automatically set Name fields based on Kategorie values when data is loaded
      if (verbrauchsdaten.ETr1_Kategorie) {
        handleKategorieChange(verbrauchsdaten.ETr1_Kategorie, 'ETr1_Name', form);
      }
      if (verbrauchsdaten.ETr2_Kategorie) {
        handleKategorieChange(verbrauchsdaten.ETr2_Kategorie, 'ETr2_Name', form);
      }
      if (verbrauchsdaten.ETr3_Kategorie) {
        handleKategorieChange(verbrauchsdaten.ETr3_Kategorie, 'ETr3_Name', form);
      }
    }
  }, [existingData, handleKategorieChange]); // Only depend on existingData and handleKategorieChange



  // Helper component for form fields
  const FormField = ({
    name,
    label,
    type = 'text',
    placeholder = '',
    required = false,
    onChangeCallback,
    unit
  }: {
    name: keyof VerbrauchFormValues;
    label: string;
    type?: string;
    placeholder?: string;
    required?: boolean;
    onChangeCallback?: (value: string) => void;
    unit?: string;
  }) => {
    const { state, handleChange, handleBlur } = useField({
      name,
      form,
    });

    const [localValidationError, setLocalValidationError] = useState<string | null>(null);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;

      // For date fields, perform immediate validation
      if (type === 'date' && value) {
        if (!validateNotFutureDate(value)) {
          setLocalValidationError('Das Datum darf nicht in der Zukunft liegen');
        } else {
          setLocalValidationError(null);
        }
      } else {
        setLocalValidationError(null);
      }

      handleChange(value);
      // Call the callback immediately for real-time updates
      if (onChangeCallback) {
        onChangeCallback(value);
      }
    };

    // Clear local validation error when field errors change (e.g., after form submission)
    useEffect(() => {
      if (state.meta.errors.length > 0) {
        setLocalValidationError(null);
      }
    }, [state.meta.errors]);

    const hasError = state.meta.errors.length > 0 || localValidationError !== null;
    const errorMessage = localValidationError || state.meta.errors.join(', ');

    return (
      <div className="mb-4">
        <label htmlFor={name} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {unit && <span className="text-gray-500">({unit})</span>} {required && <span className="text-red-500">*</span>}
        </label>
        <div className="relative">
          <input
            id={name}
            name={name}
            type={type}
            value={state.value ?? ''}
            onChange={handleInputChange}
            onBlur={handleBlur}
            placeholder={placeholder}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 transition-colors ${
              hasError
                ? 'border-red-500 focus:ring-red-500 focus:border-red-500 bg-red-50'
                : 'border-gray-300 focus:ring-green-500 focus:border-green-500'
            } ${unit ? 'pr-16' : ''}`}
            aria-invalid={hasError}
            aria-describedby={hasError ? `${name}-error` : undefined}
          />
          {unit && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <span className="text-gray-500 text-sm">{unit}</span>
            </div>
          )}
        </div>
        {hasError && (
          <div id={`${name}-error`} className="mt-1">
            <p className="text-sm text-red-600 flex items-start" role="alert">
              <svg className="h-4 w-4 text-red-500 mt-0.5 mr-1 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              {errorMessage}
            </p>
          </div>
        )}
      </div>
    );
  };




  // Helper component for Fernwärme checkbox with onChange callback
  const FernwaermeCheckboxField = ({
    name,
    label,
    form,
    onChange
  }: {
    name: keyof VerbrauchFormValues;
    label: string;
    form: any;
    onChange: (checked: boolean) => void;
  }) => {
    const { state, handleChange } = useField({
      name,
      form,
    });

    const isChecked = state.value === '1';

    const handleCheckboxChange = (checked: boolean) => {
      const value = checked ? '1' : '0';
      handleChange(value);
      onChange(checked);
    };

    return (
      <div className="mb-4">
        <div className="flex items-start">
          <input
            id={String(name)}
            name={String(name)}
            type="checkbox"
            checked={isChecked}
            onChange={(e) => handleCheckboxChange(e.target.checked)}
            className="mt-1 h-4 w-4 rounded border-gray-300 text-green-600 focus:ring-green-500 focus:ring-2 focus:ring-offset-0"
          />
          <label
            htmlFor={String(name)}
            className="ml-3 block text-sm font-medium text-gray-700"
          >
            {label}
          </label>
        </div>
      </div>
    );
  };

  // Helper component for conditional Fernwärme fields
  const ConditionalFernwaermeFields = ({
    prefix,
    showFields
  }: {
    prefix: 'ETr1' | 'ETr2' | 'ETr3';
    showFields: boolean;
  }) => {
    if (!showFields) {
      return null;
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <FormField
          name={`${prefix}_Anteil_erneuerbar` as keyof VerbrauchFormValues}
          label="Anteil erneuerbar (%)"
          placeholder="z.B. 20"
        />
        <FormField
          name={`${prefix}_Anteil_KWK` as keyof VerbrauchFormValues}
          label="KWK-Anteil (%)"
          placeholder="z.B. 30"
        />
      </div>
    );
  };









  return (
    <div className="max-w-5xl mx-auto px-2 sm:px-4 lg:px-6">
      {/* Breadcrumb Navigation */}
      <Breadcrumb className="mb-6" />

      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Energieverbrauch erfassen
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        Bitte geben Sie die Verbrauchsdaten für bis zu drei Energieträger und drei Jahre ein.
        Sie können auch Ihre Verbrauchsrechnungen als Bild oder PDF hochladen.
      </p>


      {isLoading ? (
        <div className="bg-white shadow-md rounded-lg p-6 flex justify-center items-center h-64">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500 mb-2"></div>
            <p className="text-gray-600">Daten werden geladen...</p>
          </div>
        </div>
      ) : (
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="bg-white shadow-md rounded-lg p-6"
        >
          {/* Energieträger 1 */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Energieträger 1
            </h2>

            <div className="grid grid-cols-1 gap-4 mb-6">
              <EnhancedSelectFieldWithCallback
                name="ETr1_Kategorie"
                label="Energieträger"
                options={energietraegerOptions}
                required
                onChangeCallback={(value: string) => handleKategorieChange(value, 'ETr1_Name', form)}
                form={form}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <CheckboxField
                name="ETr1_Heizung"
                label="Für Heizung verwendet"
                form={form}
              />

              <CheckboxField
                name="ETr1_TWW"
                label="Für Trinkwarmwasser verwendet"
                form={form}
              />

              {certificateType === 'NWG/V' && (
                <CheckboxField
                  name="ETr1_ZusatzHz"
                  label="Als Zusatzheizung verwendet"
                  form={form}
                />
              )}
            </div>

            {certificateType === 'NWG/V' && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <CheckboxField
                  name="ETr1_Lueften"
                  label="Für Lüftung verwendet"
                  form={form}
                />

                <CheckboxField
                  name="ETr1_Licht"
                  label="Für Beleuchtung verwendet"
                  form={form}
                />

                <CheckboxField
                  name="ETr1_Kuehlen"
                  label="Für Kühlung verwendet"
                  form={form}
                />
              </div>
            )}

            {certificateType === 'NWG/V' && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <CheckboxField
                  name="ETr1_Sonst"
                  label="Für sonstige Zwecke verwendet"
                  form={form}
                />
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <CheckboxField
                name="ETr1_gebaeudeNahErzeugt"
                label="Gebäudenah erzeugt"
                form={form}
              />

              <FernwaermeCheckboxField
                name="ETr1_isFw"
                label="Ist Fernwärme"
                form={form}
                onChange={(checked) => setEtr1IsFw(checked)}
              />
            </div>

            {/* Conditional fields for Fernwärme - only show when ETr1_isFw is checked */}
            <ConditionalFernwaermeFields
              prefix="ETr1"
              showFields={etr1IsFw}
            />

            {/* Primärenergiefaktor is fixed at 1 per business requirements - hidden from user interface */}

            {/* Jahr 1 für ETr1 */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-medium text-gray-800 mb-3">Verbrauchsdaten Jahr 1</h3>
              <p className="text-sm text-green-600 mb-3">
                💡 Tipp: Geben Sie das Enddatum des aktuellsten Verbrauchszeitraums ein. Alle anderen Zeiträume werden automatisch rückwirkend berechnet (3 aufeinanderfolgende 12-Monats-Perioden). Sie können diese anschließend bei Bedarf anpassen.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormField
                  name="ETr1_Jahr1_von"
                  label="Zeitraum von"
                  type="date"
                />

                <FormField
                  name="ETr1_Jahr1_bis"
                  label="Zeitraum bis (aktuellster Zeitraum)"
                  type="date"
                  onChangeCallback={(value) => updateCalculatedDates(value, 'ETr1', form)}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <EnhancedConsumptionAmountField
                  name="ETr1_Jahr1_Menge"
                  label="Verbrauchsmenge gesamt"
                  placeholder="z.B. 15000"
                  energietraegerFieldName="ETr1_Kategorie"
                  form={form}
                />
              </div>

              <VacancyPeriodField
                prefix="ETr1"
                jahr="Jahr1"
                form={form}
              />
            </div>

            {/* Jahr 2 für ETr1 */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-medium text-gray-800 mb-3">Verbrauchsdaten Jahr 2</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormField
                  name="ETr1_Jahr2_von"
                  label="Zeitraum von"
                  type="date"
                />

                <FormField
                  name="ETr1_Jahr2_bis"
                  label="Zeitraum bis"
                  type="date"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <EnhancedConsumptionAmountField
                  name="ETr1_Jahr2_Menge"
                  label="Verbrauchsmenge gesamt"
                  placeholder="z.B. 15000"
                  energietraegerFieldName="ETr1_Kategorie"
                  form={form}
                />
              </div>

              <VacancyPeriodField
                prefix="ETr1"
                jahr="Jahr2"
                form={form}
              />
            </div>

            {/* Jahr 3 für ETr1 */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-medium text-gray-800 mb-3">Verbrauchsdaten Jahr 3</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormField
                  name="ETr1_Jahr3_von"
                  label="Zeitraum von"
                  type="date"
                />

                <FormField
                  name="ETr1_Jahr3_bis"
                  label="Zeitraum bis"
                  type="date"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <EnhancedConsumptionAmountField
                  name="ETr1_Jahr3_Menge"
                  label="Verbrauchsmenge gesamt"
                  placeholder="z.B. 15000"
                  energietraegerFieldName="ETr1_Kategorie"
                  form={form}
                />
              </div>

              <VacancyPeriodField
                prefix="ETr1"
                jahr="Jahr3"
                form={form}
              />
            </div>
          </div>

          {/* Button to add Energieträger 2 */}
          {!showEtr2 && (
            <div className="mb-8">
              <button
                type="button"
                onClick={() => setShowEtr2(true)}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
              >
                + Weiteren Energieträger hinzufügen
              </button>
            </div>
          )}

          {/* Energieträger 2 */}
          {showEtr2 && (
            <div className="mb-8">
              <div className="flex justify-between items-center mb-4 pb-2 border-b">
                <h2 className="text-xl font-semibold text-gray-800">
                  Energieträger 2
                </h2>
                <button
                  type="button"
                  onClick={() => {
                    // Clear all ETr2 fields when removing
                    Object.keys(form.state.values).forEach(key => {
                      if (key.startsWith('ETr2_')) {
                        form.setFieldValue(key as keyof VerbrauchFormValues, '');
                      }
                    });
                    setShowEtr2(false);
                  }}
                  className="text-red-600 hover:text-red-800"
                >
                  Entfernen
                </button>
              </div>

              <div className="grid grid-cols-1 gap-4 mb-6">
                <EnhancedSelectFieldWithCallback
                  name="ETr2_Kategorie"
                  label="Energieträger"
                  options={energietraegerOptions}
                  onChangeCallback={(value: string) => handleKategorieChange(value, 'ETr2_Name', form)}
                  form={form}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <CheckboxField
                  name="ETr2_Heizung"
                  label="Für Heizung verwendet"
                  form={form}
                />

                <CheckboxField
                  name="ETr2_TWW"
                  label="Für Trinkwarmwasser verwendet"
                  form={form}
                />

                {certificateType === 'NWG/V' && (
                  <CheckboxField
                    name="ETr2_ZusatzHz"
                    label="Als Zusatzheizung verwendet"
                    form={form}
                  />
                )}
              </div>

              {certificateType === 'NWG/V' && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <CheckboxField
                    name="ETr2_Lueften"
                    label="Für Lüftung verwendet"
                    form={form}
                  />

                  <CheckboxField
                    name="ETr2_Licht"
                    label="Für Beleuchtung verwendet"
                    form={form}
                  />

                  <CheckboxField
                    name="ETr2_Kuehlen"
                    label="Für Kühlung verwendet"
                    form={form}
                  />
                </div>
              )}

              {certificateType === 'NWG/V' && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <CheckboxField
                    name="ETr2_Sonst"
                    label="Für sonstige Zwecke verwendet"
                    form={form}
                  />
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <CheckboxField
                  name="ETr2_gebaeudeNahErzeugt"
                  label="Gebäudenah erzeugt"
                  form={form}
                />

                <FernwaermeCheckboxField
                  name="ETr2_isFw"
                  label="Ist Fernwärme"
                  form={form}
                  onChange={(checked) => setEtr2IsFw(checked)}
                />
              </div>

              {/* Conditional fields for Fernwärme - only show when ETr2_isFw is checked */}
              <ConditionalFernwaermeFields
                prefix="ETr2"
                showFields={etr2IsFw}
              />

              {/* Primärenergiefaktor is fixed at 1 per business requirements - hidden from user interface */}

              {/* Jahr 1 für ETr2 */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-medium text-gray-800 mb-3">Verbrauchsdaten Jahr 1</h3>
                <p className="text-sm text-green-600 mb-3">
                  💡 Tipp: Geben Sie das Enddatum des aktuellsten Verbrauchszeitraums ein. Alle anderen Zeiträume werden automatisch rückwirkend berechnet (3 aufeinanderfolgende 12-Monats-Perioden). Sie können diese anschließend bei Bedarf anpassen.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormField
                    name="ETr2_Jahr1_von"
                    label="Zeitraum von"
                    type="date"
                  />

                  <FormField
                    name="ETr2_Jahr1_bis"
                    label="Zeitraum bis (aktuellster Zeitraum)"
                    type="date"
                    onChangeCallback={(value) => updateCalculatedDates(value, 'ETr2', form)}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <EnhancedConsumptionAmountField
                    name="ETr2_Jahr1_Menge"
                    label="Verbrauchsmenge gesamt"
                    placeholder="z.B. 15000"
                    energietraegerFieldName="ETr2_Kategorie"
                    form={form}
                  />
                </div>

                <VacancyPeriodField
                  prefix="ETr2"
                  jahr="Jahr1"
                  form={form}
                />
              </div>

              {/* Jahr 2 für ETr2 */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-medium text-gray-800 mb-3">Verbrauchsdaten Jahr 2</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormField
                    name="ETr2_Jahr2_von"
                    label="Zeitraum von"
                    type="date"
                  />

                  <FormField
                    name="ETr2_Jahr2_bis"
                    label="Zeitraum bis"
                    type="date"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <EnhancedConsumptionAmountField
                    name="ETr2_Jahr2_Menge"
                    label="Verbrauchsmenge gesamt"
                    placeholder="z.B. 15000"
                    energietraegerFieldName="ETr2_Kategorie"
                    form={form}
                  />
                </div>

                <VacancyPeriodField
                  prefix="ETr2"
                  jahr="Jahr2"
                  form={form}
                />
              </div>

              {/* Jahr 3 für ETr2 */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-medium text-gray-800 mb-3">Verbrauchsdaten Jahr 3</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormField
                    name="ETr2_Jahr3_von"
                    label="Zeitraum von"
                    type="date"
                  />

                  <FormField
                    name="ETr2_Jahr3_bis"
                    label="Zeitraum bis"
                    type="date"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <EnhancedConsumptionAmountField
                    name="ETr2_Jahr3_Menge"
                    label="Verbrauchsmenge gesamt"
                    placeholder="z.B. 15000"
                    energietraegerFieldName="ETr2_Kategorie"
                    form={form}
                  />
                </div>

                <VacancyPeriodField
                  prefix="ETr2"
                  jahr="Jahr3"
                  form={form}
                />
              </div>
            </div>
          )}

          {/* Button to add Energieträger 3 */}
          {showEtr2 && !showEtr3 && (
            <div className="mb-8">
              <button
                type="button"
                onClick={() => setShowEtr3(true)}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
              >
                + Weiteren Energieträger hinzufügen
              </button>
            </div>
          )}

          {/* Energieträger 3 */}
          {showEtr3 && (
            <div className="mb-8">
              <div className="flex justify-between items-center mb-4 pb-2 border-b">
                <h2 className="text-xl font-semibold text-gray-800">
                  Energieträger 3
                </h2>
                <button
                  type="button"
                  onClick={() => {
                    // Clear all ETr3 fields when removing
                    Object.keys(form.state.values).forEach(key => {
                      if (key.startsWith('ETr3_')) {
                        form.setFieldValue(key as keyof VerbrauchFormValues, '');
                      }
                    });
                    setShowEtr3(false);
                  }}
                  className="text-red-600 hover:text-red-800"
                >
                  Entfernen
                </button>
              </div>

              <div className="grid grid-cols-1 gap-4 mb-6">
                <EnhancedSelectFieldWithCallback
                  name="ETr3_Kategorie"
                  label="Energieträger"
                  options={energietraegerOptions}
                  onChangeCallback={(value: string) => handleKategorieChange(value, 'ETr3_Name', form)}
                  form={form}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <CheckboxField
                  name="ETr3_Heizung"
                  label="Für Heizung verwendet"
                  form={form}
                />

                <CheckboxField
                  name="ETr3_TWW"
                  label="Für Trinkwarmwasser verwendet"
                  form={form}
                />

                {certificateType === 'NWG/V' && (
                  <CheckboxField
                    name="ETr3_ZusatzHz"
                    label="Als Zusatzheizung verwendet"
                    form={form}
                  />
                )}
              </div>

              {certificateType === 'NWG/V' && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <CheckboxField
                    name="ETr3_Lueften"
                    label="Für Lüftung verwendet"
                    form={form}
                  />

                  <CheckboxField
                    name="ETr3_Licht"
                    label="Für Beleuchtung verwendet"
                    form={form}
                  />

                  <CheckboxField
                    name="ETr3_Kuehlen"
                    label="Für Kühlung verwendet"
                    form={form}
                  />
                </div>
              )}

              {certificateType === 'NWG/V' && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <CheckboxField
                    name="ETr3_Sonst"
                    label="Für sonstige Zwecke verwendet"
                    form={form}
                  />
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <CheckboxField
                  name="ETr3_gebaeudeNahErzeugt"
                  label="Gebäudenah erzeugt"
                  form={form}
                />

                <FernwaermeCheckboxField
                  name="ETr3_isFw"
                  label="Ist Fernwärme"
                  form={form}
                  onChange={(checked) => setEtr3IsFw(checked)}
                />
              </div>

              {/* Conditional fields for Fernwärme - only show when ETr3_isFw is checked */}
              <ConditionalFernwaermeFields
                prefix="ETr3"
                showFields={etr3IsFw}
              />

              {/* Primärenergiefaktor is fixed at 1 per business requirements - hidden from user interface */}

              {/* Jahr 1 für ETr3 */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-medium text-gray-800 mb-3">Verbrauchsdaten Jahr 1</h3>
                <p className="text-sm text-green-600 mb-3">
                  💡 Tipp: Geben Sie das Enddatum des aktuellsten Verbrauchszeitraums ein. Alle anderen Zeiträume werden automatisch rückwirkend berechnet (3 aufeinanderfolgende 12-Monats-Perioden). Sie können diese anschließend bei Bedarf anpassen.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormField
                    name="ETr3_Jahr1_von"
                    label="Zeitraum von"
                    type="date"
                  />

                  <FormField
                    name="ETr3_Jahr1_bis"
                    label="Zeitraum bis (aktuellster Zeitraum)"
                    type="date"
                    onChangeCallback={(value) => updateCalculatedDates(value, 'ETr3', form)}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <EnhancedConsumptionAmountField
                    name="ETr3_Jahr1_Menge"
                    label="Verbrauchsmenge gesamt"
                    placeholder="z.B. 15000"
                    energietraegerFieldName="ETr3_Kategorie"
                    form={form}
                  />
                </div>

                <VacancyPeriodField
                  prefix="ETr3"
                  jahr="Jahr1"
                  form={form}
                />
              </div>

              {/* Jahr 2 für ETr3 */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-medium text-gray-800 mb-3">Verbrauchsdaten Jahr 2</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormField
                    name="ETr3_Jahr2_von"
                    label="Zeitraum von"
                    type="date"
                  />

                  <FormField
                    name="ETr3_Jahr2_bis"
                    label="Zeitraum bis"
                    type="date"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <EnhancedConsumptionAmountField
                    name="ETr3_Jahr2_Menge"
                    label="Verbrauchsmenge gesamt"
                    placeholder="z.B. 15000"
                    energietraegerFieldName="ETr3_Kategorie"
                    form={form}
                  />
                </div>

                <VacancyPeriodField
                  prefix="ETr3"
                  jahr="Jahr2"
                  form={form}
                />
              </div>

              {/* Jahr 3 für ETr3 */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-medium text-gray-800 mb-3">Verbrauchsdaten Jahr 3</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormField
                    name="ETr3_Jahr3_von"
                    label="Zeitraum von"
                    type="date"
                  />

                  <FormField
                    name="ETr3_Jahr3_bis"
                    label="Zeitraum bis"
                    type="date"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <EnhancedConsumptionAmountField
                    name="ETr3_Jahr3_Menge"
                    label="Verbrauchsmenge gesamt"
                    placeholder="z.B. 15000"
                    energietraegerFieldName="ETr3_Kategorie"
                    form={form}
                  />
                </div>

                <VacancyPeriodField
                  prefix="ETr3"
                  jahr="Jahr3"
                  form={form}
                />
              </div>
            </div>
          )}

          {/* Verbrauchsrechnungen Upload */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Verbrauchsrechnungen hochladen
            </h2>
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="text-lg font-medium text-blue-800 mb-2">Flexible Upload-Optionen</h3>
              <p className="text-blue-700 mb-3">
                Laden Sie Ihre Verbrauchsrechnungen der letzten drei Jahre in einem der folgenden Formate hoch:
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
                <div>
                  <h4 className="font-medium mb-2">📄 PDF-Option:</h4>
                  <ul className="space-y-1 text-xs">
                    <li>• Eine PDF-Datei pro Jahr</li>
                    <li>• Alle Rechnungen in einem Dokument</li>
                    <li>• Maximale Dateigröße: 5MB</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">🖼️ Bild-Option:</h4>
                  <ul className="space-y-1 text-xs">
                    <li>• Bis zu 5 Bilder pro Jahr</li>
                    <li>• Formate: JPG, PNG, WebP</li>
                    <li>• Max. 5MB pro Bild, 20MB pro Jahr</li>
                  </ul>
                </div>
              </div>
              <p className="text-xs text-blue-600 mt-3">
                <strong>Gesamtlimit:</strong> Maximal 50MB für alle Uploads zusammen
              </p>
            </div>

            <div className="space-y-4">
              <DirectoryBasedFileUpload
                certificateId={memoizedCertificateId}
                fieldName="verbrauchsrechnung1"
                label="Verbrauchsrechnung Jahr 1"
              />

              <DirectoryBasedFileUpload
                certificateId={memoizedCertificateId}
                fieldName="verbrauchsrechnung2"
                label="Verbrauchsrechnung Jahr 2"
              />

              <DirectoryBasedFileUpload
                certificateId={memoizedCertificateId}
                fieldName="verbrauchsrechnung3"
                label="Verbrauchsrechnung Jahr 3"
              />
            </div>
          </div>

          {submitError && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {submitError}
            </div>
          )}

          {/* Validation Error Summary */}
          {Object.keys(validationErrors).length > 0 && (
            <ValidationErrorSummary errors={validationErrors} />
          )}

          <div className="flex justify-between mt-8">
            <Link
              to={certificateType === 'WG/B' ? '/erfassen/tww-lueftung' : '/erfassen/gebaeudedetails2'}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
            >
              Zurück
            </Link>
            <button
              type="submit"
              disabled={form.state.isSubmitting || saveMutation.isPending}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:bg-green-300"
            >
              {form.state.isSubmitting || saveMutation.isPending
                ? 'Wird gespeichert...'
                : 'Weiter zur Zusammenfassung'
              }
            </button>
          </div>
        </form>
      )}

      {isError && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <p>Fehler beim Laden der Daten: {error instanceof Error ? error.message : 'Unbekannter Fehler'}</p>
          <p className="mt-2">Bitte versuchen Sie es später erneut oder kontaktieren Sie den Support.</p>
        </div>
      )}
    </div>
  );
};