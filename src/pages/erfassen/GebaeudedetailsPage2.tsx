import { useState, useEffect } from "react";
import { useForm, useField } from "@tanstack/react-form";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { z } from "zod";
import { Link, useNavigate } from "@tanstack/react-router";
import { supabase } from "../../lib/supabase";
import { useCertificate } from "../../contexts/CertificateContext";
import { CheckboxField } from "../../components/ui/CheckboxField";
import { Breadcrumb } from "../../components/ui/Breadcrumb";
import { usePageVisit } from "../../hooks/usePageVisit";
import { useNavigationState } from "../../hooks/useNavigationState";
import { ValidationErrorSummary } from "../../components/ui/ValidationErrorSummary";
import {
  handleZodValidationError,
  clearFormFieldErrors,
} from "../../utils/formValidation";

// Define interfaces for the JSON data from Supabase
interface TrinkwarmwasserData {
  TW_Solar?: string;
  HZ_Solar?: string;
  TW_WP?: string;
  HZ_WP?: string;
  [key: string]: any; // Allow other properties
}

// Define interface for gebaeudedetails2 data
interface Gebaeudedetails2Data {
  kuehlWfl?: string;
  Originaldaemmstandard?: string;
  bjFensterAustausch?: string;
  Fensterlüftung?: string;
  Schachtlüftung?: string;
  L_Mit_WRG?: string;
  L_Ohne_WRG?: string;
  Keller_beheizt?: string;
  Klimatisiert?: string;
  Boden1_Dämmung?: string;
  Dach1_Dämmung?: string;
  Wand1_Dämmung?: string;
  boeden?: Bauteil[];
  daecher?: Bauteil[];
  [key: string]: any; // Allow other properties
}

// Define the Bauteil schema for reuse
const bauteilSchema = z.object({
  id: z.string(),
  bezeichnung: z.string().min(1, "Bezeichnung ist erforderlich"),
  massiv: z.string().optional(),
  uebergang: z.string().optional(),
  flaeche: z.string().min(1, "Fläche ist erforderlich"),
  uWert: z.string().optional(),
  // Intermediate calculation fields for wall area calculator
  wallWidth: z.string().optional(),
  numberOfLevels: z.string().optional(),
  // Removed daemmung field as it's now tracked separately
});

// Define the form schema using Zod
const gebaeudedetails2Schema = z.object({
  // Gebäudedetails Teil 2
  kuehlWfl: z.string().default("0"),
  Originaldaemmstandard: z.enum(["0", "1", "2"]).default("0"),
  bjFensterAustausch: z.string().optional(),
  Fensterlüftung: z.enum(["0", "1"]).default("0"),
  Schachtlüftung: z.enum(["0", "1"]).default("0"),
  L_Mit_WRG: z.enum(["0", "1"]).default("0"),
  L_Ohne_WRG: z.enum(["0", "1"]).default("0"),

  // Moved from GebaeudedetailsPage1
  Keller_beheizt: z.enum(["0", "1"]).default("0").optional(),
  Klimatisiert: z.enum(["0", "1"]).default("0"),

  // Erneuerbare Energien Felder (von TwwLueftungPage verschoben)
  TW_Solar: z.enum(["0", "1"]).default("0"),
  HZ_Solar: z.enum(["0", "1"]).default("0"),
  TW_WP: z.enum(["0", "1"]).default("0"),
  HZ_WP: z.enum(["0", "1"]).default("0"),

  // Dämmungsfelder für alle Zertifikatstypen
  Boden1_Dämmung: z.string().default("0"),
  Dach1_Dämmung: z.string().default("0"),
  Wand1_Dämmung: z.string().default("0"),

  // Bauteile (dynamisch)
  boeden: z.array(bauteilSchema).default([]),
  daecher: z.array(bauteilSchema).default([]),
});

type Gebaeudedetails2FormValues = z.infer<typeof gebaeudedetails2Schema>;
type Bauteil = z.infer<typeof bauteilSchema>;

export const GebaeudedetailsPage2 = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string[]>
  >({});
  const [isLoading, setIsLoading] = useState(true);
  const [certificateType, setCertificateType] = useState<
    "WG/V" | "WG/B" | "NWG/V" | null
  >(null);

  // Mark this page as visited for navigation tracking
  usePageVisit("gebaeudedetails2");
  const { activeCertificateId } = useCertificate();
  const { markPageAsVisited } = useNavigationState(certificateType);

  // Helper function to ensure unique IDs for building components
  const ensureUniqueIds = (bauteile: Bauteil[]): Bauteil[] => {
    return bauteile.map((bauteil, index) => ({
      ...bauteil,
      id:
        bauteil.id ||
        `${Date.now()}-${index}-${Math.random().toString(36).substring(2, 11)}`,
    }));
  };

  // Helper function to create default Bauteile with numberOfLevels from gebaeudedetails1
  const createDefaultBoeden = (numberOfLevels: string = "") => [
    {
      id: "default-boden-1",
      bezeichnung: "Boden1",
      massiv: "kb_Massiv",
      uebergang: "1",
      flaeche: "",
      uWert: "",
      wallWidth: "",
      numberOfLevels: numberOfLevels,
    },
  ];
  const createDefaultDaecher = (numberOfLevels: string = "") => [
    {
      id: "default-dach-1",
      bezeichnung: "Dach1",
      massiv: "0",
      uebergang: "0",
      flaeche: "",
      uWert: "",
      wallWidth: "",
      numberOfLevels: numberOfLevels,
    },
  ];

  // Default Bauteile with unique IDs (will be updated when existingData is available)
  const defaultBoeden = createDefaultBoeden();
  const defaultDaecher = createDefaultDaecher();
  // State für dynamische Bauteile
  const [boeden, setBoeden] = useState<Bauteil[]>(defaultBoeden);
  const [daecher, setDaecher] = useState<Bauteil[]>(defaultDaecher);

  // Initial form values
  const [initialValues, setInitialValues] = useState<
    Partial<Gebaeudedetails2FormValues>
  >(() => {
    // Base values that are always included
    const baseValues = {
      kuehlWfl: "0",
      Originaldaemmstandard: "0" as const,
      bjFensterAustausch: "",
      Fensterlüftung: "0" as const,
      Schachtlüftung: "0" as const,
      L_Mit_WRG: "0" as const,
      L_Ohne_WRG: "0" as const,
      // Moved from GebaeudedetailsPage1
      Keller_beheizt: "0" as const,
      Klimatisiert: "0" as const,
      // Erneuerbare Energien Felder (von TwwLueftungPage verschoben)
      TW_Solar: "0" as const,
      HZ_Solar: "0" as const,
      TW_WP: "0" as const,
      HZ_WP: "0" as const,
      // Dämmungsfelder für alle Zertifikatstypen
      Boden1_Dämmung: "0",
      Dach1_Dämmung: "0",
      Wand1_Dämmung: "0",
    };

    // Only include building components if certificate type is WG/B
    if (certificateType === "WG/B") {
      return {
        ...baseValues,
        boeden: boeden,
        daecher: daecher,
      };
    }

    return baseValues;
  });

  // Fetch existing data including certificate type, heizung, trinkwarmwasser, and gebaeudedetails1 (for migration)
  const {
    data: existingData,
    isError,
    error,
  } = useQuery({
    queryKey: [
      "energieausweise",
      "gebaeudedetails2",
      "gebaeudedetails1",
      "heizung",
      "trinkwarmwasser",
      activeCertificateId,
    ],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from("energieausweise")
        .select(
          "gebaeudedetails2, gebaeudedetails1, certificate_type, heizung, trinkwarmwasser",
        )
        .eq("id", activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Update form values when data is fetched
  useEffect(() => {
    if (existingData) {
      // Type assertion to ensure certificate_type matches the expected union type
      const certType = existingData.certificate_type as
        | "WG/V"
        | "WG/B"
        | "NWG/V";

      // Set certificate type if it exists
      if (existingData.certificate_type) {
        setCertificateType(certType);
      }

      // Extract renewable energy fields from trinkwarmwasser data
      // Type assertion to tell TypeScript about the structure
      const trinkwarmwasser =
        existingData.trinkwarmwasser as TrinkwarmwasserData | null;

      // Ensure values match the enum types defined in the schema
      const ensureEnumValue = (value: string | undefined): "0" | "1" =>
        value === "1" ? "1" : "0";

      const renewableEnergyFields = {
        TW_Solar: ensureEnumValue(trinkwarmwasser?.TW_Solar),
        HZ_Solar: ensureEnumValue(trinkwarmwasser?.HZ_Solar),
        TW_WP: ensureEnumValue(trinkwarmwasser?.TW_WP),
        HZ_WP: ensureEnumValue(trinkwarmwasser?.HZ_WP),
      };

      // Handle migration from gebaeudedetails1 if fields don't exist in gebaeudedetails2 yet
      const gebaeudedetails1Data = existingData.gebaeudedetails1 as any;

      if (existingData.gebaeudedetails2) {
        // Type assertion to tell TypeScript about the structure
        const data = existingData.gebaeudedetails2 as Gebaeudedetails2Data;

        // Update dynamic components based on certificate type
        if (certType === "WG/B") {
          // Get numberOfLevels from gebaeudedetails1 to ensure consistency
          const gebaeudedetails1Data = existingData.gebaeudedetails1 as any;
          const numberOfLevels = gebaeudedetails1Data?.Geschosse || "";

          // Set building components if certificate type is WG/B, ensuring unique IDs and numberOfLevels
          if (data.boeden && Array.isArray(data.boeden)) {
            const updatedBoeden = data.boeden.map((boden: Bauteil) => ({
              ...boden,
              numberOfLevels: boden.numberOfLevels || numberOfLevels,
            }));
            setBoeden(ensureUniqueIds(updatedBoeden));
          } else {
            // Create default boeden with numberOfLevels
            setBoeden(createDefaultBoeden(numberOfLevels));
          }

          if (data.daecher && Array.isArray(data.daecher)) {
            const updatedDaecher = data.daecher.map((dach: Bauteil) => ({
              ...dach,
              numberOfLevels: dach.numberOfLevels || numberOfLevels,
            }));
            setDaecher(ensureUniqueIds(updatedDaecher));
          } else {
            // Create default daecher with numberOfLevels
            setDaecher(createDefaultDaecher(numberOfLevels));
          }
        } else {
          // Reset building components to empty arrays if not WG/B
          setBoeden([]);
          setDaecher([]);
        }

        // Use the standalone dämmung fields or default to '0'
        const extractedDaemmungFields = {
          Boden1_Dämmung: data.Boden1_Dämmung || "0",
          Dach1_Dämmung: data.Dach1_Dämmung || "0",
          Wand1_Dämmung: data.Wand1_Dämmung || "0",
        };

        // Ensure enum values match the schema
        const ensureOriginaldaemmstandard = (
          value: string | undefined,
        ): "0" | "1" | "2" => {
          if (value === "1") return "1";
          if (value === "2") return "2";
          return "0";
        };

        // Create a sanitized version of the data with proper types
        // Include migration from gebaeudedetails1 if fields don't exist in gebaeudedetails2
        const sanitizedData: Partial<Gebaeudedetails2FormValues> = {
          kuehlWfl: data.kuehlWfl || "0",
          Originaldaemmstandard: ensureOriginaldaemmstandard(
            data.Originaldaemmstandard,
          ),
          bjFensterAustausch: data.bjFensterAustausch || "",
          Fensterlüftung: ensureEnumValue(data.Fensterlüftung),
          Schachtlüftung: ensureEnumValue(data.Schachtlüftung),
          L_Mit_WRG: ensureEnumValue(data.L_Mit_WRG),
          L_Ohne_WRG: ensureEnumValue(data.L_Ohne_WRG),
          // Migrate from gebaeudedetails1 if not present in gebaeudedetails2
          Keller_beheizt: ensureEnumValue(
            data.Keller_beheizt || gebaeudedetails1Data?.Keller_beheizt,
          ),
          Klimatisiert: ensureEnumValue(
            data.Klimatisiert || gebaeudedetails1Data?.Klimatisiert,
          ),
        };

        // Update form values based on certificate type
        if (certType === "WG/B") {
          setInitialValues((prev) => ({
            ...prev,
            ...sanitizedData,
            ...extractedDaemmungFields,
            ...renewableEnergyFields,
            boeden:
              data.boeden && Array.isArray(data.boeden)
                ? ensureUniqueIds(data.boeden)
                : boeden,
            daecher:
              data.daecher && Array.isArray(data.daecher)
                ? ensureUniqueIds(data.daecher)
                : daecher,
          }));
        } else {
          // Exclude building components if not WG/B, but keep the dämmung fields
          setInitialValues((prev) => ({
            ...prev,
            ...sanitizedData,
            ...extractedDaemmungFields,
            ...renewableEnergyFields,
          }));
        }
      }
    }
    setIsLoading(false);
  }, [existingData]);

  // Update initialValues when certificateType changes (but not when building components change)
  useEffect(() => {
    setInitialValues((prev) => {
      // Preserve the dämmung fields regardless of certificate type
      const daemmungFields = {
        Boden1_Dämmung: prev.Boden1_Dämmung || "0",
        Dach1_Dämmung: prev.Dach1_Dämmung || "0",
        Wand1_Dämmung: prev.Wand1_Dämmung || "0",
      };

      // Ensure enum values are properly typed
      const ensureEnumValue = (value: string | undefined): "0" | "1" =>
        value === "1" ? "1" : "0";

      const ensureOriginaldaemmstandard = (
        value: string | undefined,
      ): "0" | "1" | "2" => {
        if (value === "1") return "1";
        if (value === "2") return "2";
        return "0";
      };

      // Create a sanitized version of the previous state
      const sanitizedPrev: Partial<Gebaeudedetails2FormValues> = {
        kuehlWfl: prev.kuehlWfl || "0",
        Originaldaemmstandard: ensureOriginaldaemmstandard(
          prev.Originaldaemmstandard,
        ),
        bjFensterAustausch: prev.bjFensterAustausch || "",
        Fensterlüftung: ensureEnumValue(prev.Fensterlüftung),
        Schachtlüftung: ensureEnumValue(prev.Schachtlüftung),
        L_Mit_WRG: ensureEnumValue(prev.L_Mit_WRG),
        L_Ohne_WRG: ensureEnumValue(prev.L_Ohne_WRG),
        Keller_beheizt: ensureEnumValue(prev.Keller_beheizt),
        Klimatisiert: ensureEnumValue(prev.Klimatisiert),
        TW_Solar: ensureEnumValue(prev.TW_Solar),
        HZ_Solar: ensureEnumValue(prev.HZ_Solar),
        TW_WP: ensureEnumValue(prev.TW_WP),
        HZ_WP: ensureEnumValue(prev.HZ_WP),
      };

      if (certificateType === "WG/B") {
        // For WG/B, preserve existing building component values from form or use current state
        const currentBoeden = prev.boeden || boeden;
        const currentDaecher = prev.daecher || daecher;

        return {
          ...sanitizedPrev,
          ...daemmungFields,
          boeden: currentBoeden,
          daecher: currentDaecher,
        };
      } else {
        // Create a new object without the building component properties
        // but keep the dämmung fields
        return {
          ...sanitizedPrev,
          ...daemmungFields,
        };
      }
    });
  }, [certificateType]); // Removed boeden, daecher from dependencies to prevent form resets

  // Define the mutation for saving data to Supabase
  const saveMutation = useMutation({
    mutationFn: async (data: Gebaeudedetails2FormValues) => {
      // Extract fields that need to be saved in different objects
      const { TW_Solar, HZ_Solar, TW_WP, HZ_WP, ...gebaeudedetails2Data } =
        data;

      if (!activeCertificateId)
        throw new Error("Kein aktives Zertifikat ausgewählt.");

      // First, get any existing trinkwarmwasser data
      const { data: existingData } = await supabase
        .from("energieausweise")
        .select("trinkwarmwasser")
        .eq("id", activeCertificateId)
        .single();

      // Prepare trinkwarmwasser data with renewable energy fields
      const trinkwarmwasserData = {
        ...((existingData?.trinkwarmwasser as TrinkwarmwasserData | null) ||
          {}),
        TW_Solar,
        HZ_Solar,
        TW_WP,
        HZ_WP,
      };

      // Update gebaeudedetails2 and trinkwarmwasser in one transaction
      const { data: result, error } = await supabase
        .from("energieausweise")
        .update({
          gebaeudedetails2: gebaeudedetails2Data,
          trinkwarmwasser: trinkwarmwasserData,
          updated_at: new Date().toISOString(),
        })
        .eq("id", activeCertificateId)
        .select();

      if (error) throw error;
      return result;
    },
    onSuccess: async () => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ["energieausweise", activeCertificateId],
      });
      queryClient.invalidateQueries({
        queryKey: [
          "energieausweise",
          "gebaeudedetails2",
          "gebaeudedetails1",
          "heizung",
          "trinkwarmwasser",
          activeCertificateId,
        ],
      });

      // Update navigation state to mark next page as current based on certificate type
      if (certificateType === "WG/B") {
        await markPageAsVisited("gebaeudeform");
        navigate({ to: "/erfassen/gebaeudeform" });
      } else {
        // Skip tww-lueftung page for non-WG/B certificate types and go directly to verbrauch
        await markPageAsVisited("verbrauch");
        navigate({ to: "/erfassen/verbrauch" });
      }
    },
    onError: (error) => {
      setSubmitError(`Fehler beim Speichern: ${error.message}`);
    },
  });

  // Create the form
  const form = useForm({
    defaultValues: initialValues,
    onSubmit: async ({ value }) => {
      setSubmitError(null);
      setValidationErrors({});

      // Check if certificate type is loaded
      if (!certificateType) {
        setSubmitError(
          "Zertifikatstyp wird noch geladen. Bitte warten Sie einen Moment.",
        );
        return;
      }

      // Clear any existing field errors
      const fieldNames = Object.keys(gebaeudedetails2Schema.shape);
      clearFormFieldErrors(form, fieldNames);

      try {
        // Prepare data for validation based on certificate type
        let dataForValidation = { ...value };

        // For non-WG/B certificate types, set empty arrays for building components to avoid validation errors
        if (certificateType !== "WG/B") {
          dataForValidation = {
            ...dataForValidation,
            boeden: [],
            daecher: [],
          };
        } else {
          // For WG/B, ensure building components are arrays
          dataForValidation = {
            ...dataForValidation,
            boeden: Array.isArray(value.boeden) ? value.boeden : [],
            daecher: Array.isArray(value.daecher) ? value.daecher : [],
          };
        }

        // First validate the form data with Zod schema
        const validatedValues = gebaeudedetails2Schema.parse(dataForValidation);

        // After validation, prepare data for submission by removing certificate-type specific fields
        let dataToSubmit: any = { ...validatedValues };

        // Remove bjFensterAustausch field if certificate type is not WG/V
        if (
          certificateType !== "WG/V" &&
          "bjFensterAustausch" in dataToSubmit
        ) {
          const { bjFensterAustausch, ...rest } = dataToSubmit;
          dataToSubmit = rest;
        }

        // Remove kuehlWfl field if certificate type is NWG/V
        if (certificateType === "NWG/V" && "kuehlWfl" in dataToSubmit) {
          const { kuehlWfl, ...rest } = dataToSubmit;
          dataToSubmit = rest;
        }

        // Remove Keller_beheizt field if certificate type is not WG/V
        if (certificateType !== "WG/V" && "Keller_beheizt" in dataToSubmit) {
          const { Keller_beheizt, ...rest } = dataToSubmit;
          dataToSubmit = rest;
        }

        // For WG/B certificate type, we need to remove HZ_Solar, TW_WP, and HZ_WP fields
        if (certificateType === "WG/B") {
          // Keep TW_Solar but remove the other renewable energy fields
          const { HZ_Solar, TW_WP, HZ_WP, ...rest } = dataToSubmit;
          dataToSubmit = rest;
        }

        // For non-WG/B certificate types, we need to:
        // 1. Remove the building component arrays
        // 2. Keep the specific dämmung fields
        if (certificateType !== "WG/B") {
          // Extract the dämmung fields we want to keep
          const { Boden1_Dämmung, Dach1_Dämmung, Wand1_Dämmung } = dataToSubmit;

          // Remove boeden, daecher fields
          const { boeden, daecher, ...rest } = dataToSubmit;

          // Create the final data object with the dämmung fields preserved
          dataToSubmit = {
            ...rest,
            Boden1_Dämmung,
            Dach1_Dämmung,
            Wand1_Dämmung,
          };
        }

        // Submit the validated and processed data
        saveMutation.mutate(dataToSubmit as Gebaeudedetails2FormValues);
      } catch (validationError) {
        if (validationError instanceof z.ZodError) {
          // Use the improved validation error handling
          const { fieldErrors, summaryMessage } = handleZodValidationError(
            validationError,
            form,
          );
          setValidationErrors(fieldErrors);
          setSubmitError(summaryMessage);

          // Log for debugging
          console.error(
            "Form validation error:",
            validationError.flatten().fieldErrors,
          );
        } else {
          setSubmitError(
            "Ein unerwarteter Validierungsfehler ist aufgetreten.",
          );
        }
      }
    },
  });

  // Update form values when initialValues change
  useEffect(() => {
    if (initialValues && Object.keys(initialValues).length > 0) {
      // Update form values with the new initial values
      Object.entries(initialValues).forEach(([key, value]) => {
        if (value !== undefined) {
          try {
            form.setFieldValue(
              key as keyof Gebaeudedetails2FormValues,
              value as any,
            );
          } catch (error) {
            console.warn(`Failed to set form field ${key}:`, error);
          }
        }
      });
    }
  }, [initialValues, form]);

  // Helper function to safely get array values from form
  const getSafeArrayValue = (fieldName: "boeden" | "daecher"): Bauteil[] => {
    const formValue = form.getFieldValue(fieldName);
    const fallbackValue = fieldName === "boeden" ? boeden : daecher;

    // If form value exists and is an array, use it
    if (Array.isArray(formValue)) {
      return formValue;
    }

    // If form value is undefined or not an array, use fallback
    if (Array.isArray(fallbackValue)) {
      return fallbackValue;
    }

    // Last resort: return empty array
    console.warn(
      `Both form value and fallback for ${fieldName} are not arrays. Using empty array.`,
    );
    return [];
  };

  // Watch the Klimatisiert field for immediate reactivity
  const klimatisiertField = useField({
    name: "Klimatisiert",
    form,
  });

  // Helper component for form fields
  const FormField = ({
    name,
    label,
    type = "text",
    placeholder = "",
    required = true,
  }: {
    name: keyof Gebaeudedetails2FormValues;
    label: string;
    type?: string;
    placeholder?: string;
    required?: boolean;
  }) => {
    const { state, handleChange, handleBlur } = useField({
      name,
      form,
    });

    return (
      <div className="mb-4">
        <label
          htmlFor={name}
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <input
          id={name}
          name={name}
          type={type}
          value={
            typeof state.value === "boolean"
              ? String(state.value)
              : Array.isArray(state.value)
                ? "" // Don't try to display array values in a text input
                : (state.value ?? "")
          }
          onChange={(e) => handleChange(e.target.value)}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
            state.meta.errors.length > 0 ? "border-red-500" : "border-gray-300"
          }`}
        />
        {state.meta.errors.length > 0 && (
          <p className="mt-1 text-sm text-red-500">
            {state.meta.errors.join(", ")}
          </p>
        )}
      </div>
    );
  };

  // Helper component for select fields
  const SelectField = ({
    name,
    label,
    options,
    required = true,
  }: {
    name: keyof Gebaeudedetails2FormValues;
    label: string;
    options: { value: string; label: string }[];
    required?: boolean;
  }) => {
    const { state, handleChange, handleBlur } = useField({
      name,
      form,
    });

    return (
      <div className="mb-4">
        <label
          htmlFor={name}
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <select
          id={name}
          name={name}
          value={
            typeof state.value === "boolean"
              ? String(state.value)
              : Array.isArray(state.value)
                ? "" // Don't try to display array values in a select
                : (state.value ?? "")
          }
          onChange={(e) => handleChange(e.target.value)}
          onBlur={handleBlur}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
            state.meta.errors.length > 0 ? "border-red-500" : "border-gray-300"
          }`}
        >
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {state.meta.errors.length > 0 && (
          <p className="mt-1 text-sm text-red-500">
            {state.meta.errors.join(", ")}
          </p>
        )}
      </div>
    );
  };

  // Bauteil-Komponente für dynamische Bauteile
  const BauteilField = ({
    index,
    type,
    onRemove,
  }: {
    index: number;
    type: "boden" | "dach";
    onRemove: (index: number) => void;
  }) => {
    // Get the array field name based on type
    const arrayField = type === "boden" ? "boeden" : "daecher";

    // Use Tanstack Form's useField for each field
    const bezeichnungField = useField({
      name: `${arrayField}[${index}].bezeichnung` as const,
      form,
    });

    const massivField = useField({
      name: `${arrayField}[${index}].massiv` as const,
      form,
    });

    const uebergangField =
      type === "boden" || type === "dach"
        ? useField({
            name: `${arrayField}[${index}].uebergang` as const,
            form,
          })
        : null;

    const flaecheField = useField({
      name: `${arrayField}[${index}].flaeche` as const,
      form,
    });

    // uWertField removed from UI but uWert property maintained in schema for database compatibility

    // We no longer need to access the daemmung field for each bauteil
    // as it's now only stored in the standalone fields

    return (
      <div className="p-4 mb-4 border rounded-md bg-gray-50">
        <div className="flex justify-between mb-2">
          <h4 className="font-medium">
            {type === "boden" ? "Boden" : "Dach"} {index + 1}
          </h4>
          {/* Show remove button if index > 0 */}
          {index > 0 && (
            <button
              type="button"
              onClick={() => onRemove(index)}
              className="text-red-500 hover:text-red-700"
            >
              Entfernen
            </button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="mb-2">
            <label
              htmlFor={`${type}-${index}-bezeichnung`}
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Bezeichnung <span className="text-red-500">*</span>
            </label>
            <input
              id={`${type}-${index}-bezeichnung`}
              type="text"
              value={bezeichnungField.state.value ?? ""}
              onChange={(e) => bezeichnungField.handleChange(e.target.value)}
              onBlur={bezeichnungField.handleBlur}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
                bezeichnungField.state.meta.errors.length > 0
                  ? "border-red-500"
                  : "border-gray-300"
              }`}
              placeholder={`${type === "boden" ? "z.B. Kellerdecke" : "z.B. Kehlbalkendecke"}`}
            />
            {bezeichnungField.state.meta.errors.length > 0 && (
              <p className="mt-1 text-sm text-red-500">
                {bezeichnungField.state.meta.errors.join(", ")}
              </p>
            )}
          </div>

          <div className="mb-2">
            <label
              htmlFor={`${type}-${index}-massiv`}
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Material
            </label>
            <select
              id={`${type}-${index}-massiv`}
              value={massivField.state.value ?? ""}
              onChange={(e) => massivField.handleChange(e.target.value)}
              onBlur={massivField.handleBlur}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
                massivField.state.meta.errors.length > 0
                  ? "border-red-500"
                  : "border-gray-300"
              }`}
            >
              {type === "boden" && (
                <>
                  <option value="kb_Massiv">Ziegel/Hohlstein</option>
                  <option value="kb_Holz">Holz</option>
                  <option value="kb_Stahlbeton">Stahlbeton</option>
                </>
              )}
              {type === "dach" && (
                <>
                  <option value="1">Massivdecke</option>
                  <option value="0">Holzbalken</option>
                </>
              )}
            </select>
            {massivField.state.meta.errors.length > 0 && (
              <p className="mt-1 text-sm text-red-500">
                {massivField.state.meta.errors.join(", ")}
              </p>
            )}
          </div>

          {(type === "boden" || type === "dach") && uebergangField && (
            <div className="mb-2">
              <label
                htmlFor={`${type}-${index}-uebergang`}
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Übergang
              </label>
              <select
                id={`${type}-${index}-uebergang`}
                value={uebergangField.state.value ?? ""}
                onChange={(e) => uebergangField.handleChange(e.target.value)}
                onBlur={uebergangField.handleBlur}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  uebergangField.state.meta.errors.length > 0
                    ? "border-red-500"
                    : "border-gray-300"
                }`}
              >
                {type === "boden" && (
                  <>
                    <option value="1">Übergang zu unbeheiztem Keller</option>
                    <option value="0">Übergang zu Erdreich</option>
                  </>
                )}
                {type === "dach" && (
                  <>
                    <option value="0">Übergang zu unbeheiztem Dachraum</option>
                    <option value="1">Direkt bewittert</option>
                  </>
                )}
              </select>
              {uebergangField.state.meta.errors.length > 0 && (
                <p className="mt-1 text-sm text-red-500">
                  {uebergangField.state.meta.errors.join(", ")}
                </p>
              )}
            </div>
          )}

          {/* Area input for floors and roofs */}
          {
            <div className="mb-2">
              <label
                htmlFor={`${type}-${index}-flaeche`}
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Fläche in m² <span className="text-red-500">*</span>
              </label>
              <input
                id={`${type}-${index}-flaeche`}
                type="text"
                value={flaecheField.state.value ?? ""}
                onChange={(e) => flaecheField.handleChange(e.target.value)}
                onBlur={flaecheField.handleBlur}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  flaecheField.state.meta.errors.length > 0
                    ? "border-red-500"
                    : "border-gray-300"
                }`}
                placeholder="z.B. 144"
              />
              {flaecheField.state.meta.errors.length > 0 && (
                <p className="mt-1 text-sm text-red-500">
                  {flaecheField.state.meta.errors.join(", ")}
                </p>
              )}
            </div>
          }

          {/* U-Wert field removed from UI but maintained in schema for database compatibility */}

          {/* Dämmung field removed as it's now only stored in the standalone fields */}
        </div>
      </div>
    );
  };

  // Field references are not needed here as we use the FormField component directly

  // Funktionen zum Hinzufügen und Entfernen von Bauteilen
  const addBauteil = (type: "boden" | "dach") => {
    // Get numberOfLevels from gebaeudedetails1 data
    const gebaeudedetails1Data = existingData?.gebaeudedetails1 as any;
    const numberOfLevels = gebaeudedetails1Data?.Geschosse || "";

    if (type === "boden") {
      // Get current form values to preserve user input
      const currentFormValues = getSafeArrayValue("boeden");
      const newBoeden = [...currentFormValues];
      // Use timestamp + random number for truly unique IDs
      const newId = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      newBoeden.push({
        id: newId,
        bezeichnung: `Boden${newBoeden.length + 1}`,
        massiv: "kb_Massiv",
        uebergang: "1",
        flaeche: "",
        uWert: "",
        wallWidth: "",
        numberOfLevels: numberOfLevels,
      });
      setBoeden(newBoeden);
      form.setFieldValue("boeden", newBoeden);
    } else if (type === "dach") {
      // Get current form values to preserve user input
      const currentFormValues = getSafeArrayValue("daecher");
      const newDaecher = [...currentFormValues];
      // Use timestamp + random number for truly unique IDs
      const newId = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      newDaecher.push({
        id: newId,
        bezeichnung: `Dach${newDaecher.length + 1}`,
        massiv: "0",
        uebergang: "0",
        flaeche: "",
        uWert: "",
        wallWidth: "",
        numberOfLevels: numberOfLevels,
      });
      setDaecher(newDaecher);
      form.setFieldValue("daecher", newDaecher);
    }
  };

  const removeBauteil = (type: "boden" | "dach", index: number) => {
    if (type === "boden") {
      // Get current form values to preserve user input
      const currentFormValues = getSafeArrayValue("boeden");
      const newBoeden = [...currentFormValues];
      newBoeden.splice(index, 1);
      setBoeden(newBoeden);
      form.setFieldValue("boeden", newBoeden);
      // No need to sync daemmung values anymore
    } else if (type === "dach") {
      // Get current form values to preserve user input
      const currentFormValues = getSafeArrayValue("daecher");
      const newDaecher = [...currentFormValues];
      newDaecher.splice(index, 1);
      setDaecher(newDaecher);
      form.setFieldValue("daecher", newDaecher);
      // No need to sync daemmung values anymore
    }
  };

  return (
    <div className="max-w-5xl mx-auto px-2 sm:px-4 lg:px-6">
      {/* Breadcrumb Navigation */}
      <Breadcrumb className="mb-6" />

      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Gebäudedetails erfassen (Teil 2)
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        Bitte geben Sie die Daten zur Gebäudehülle und Dämmung ein.
      </p>

      {isLoading ? (
        <div className="bg-white shadow-md rounded-lg p-6 flex justify-center items-center h-64">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500 mb-2"></div>
            <p className="text-gray-600">Daten werden geladen...</p>
          </div>
        </div>
      ) : (
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="bg-white shadow-md rounded-lg p-6"
        >
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Kühlung und Lüftung
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Building insulation standard */}
              <SelectField
                name="Originaldaemmstandard"
                label="Originaldämmstandard"
                options={[
                  { value: "0", label: "Normal" },
                  { value: "1", label: "Niedrigenergiehaus" },
                  { value: "2", label: "Passivhaus" },
                ]}
                required={false}
              />

              {/* Window replacement year - WG/V specific */}
              {certificateType === "WG/V" && (
                <FormField
                  name="bjFensterAustausch"
                  label="Jahr des Fensteraustauschs"
                  placeholder="z.B. 2010"
                  required={false}
                />
              )}

              {/* Basement heating - WG/V specific */}
              {certificateType === "WG/V" && (
                <CheckboxField
                  name="Keller_beheizt"
                  label="Keller beheizt"
                  form={form}
                  required={false}
                />
              )}

              {/* Air conditioning section - grouped together */}
              <div className="md:col-span-2">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <CheckboxField
                    name="Klimatisiert"
                    label="Klimaanlage vorhanden"
                    form={form}
                    required={false}
                  />

                  {/* Cooling area - appears immediately when air conditioning is selected */}
                  {(certificateType === "WG/V" || certificateType === "WG/B") &&
                    klimatisiertField.state.value === "1" && (
                      <FormField
                        name="kuehlWfl"
                        label="Kühlfläche aller gekühlten Räume in m²"
                        placeholder="z.B. 25"
                        required={false}
                      />
                    )}
                </div>
              </div>

              {/* Ventilation section - grouped together */}
              <div className="md:col-span-2">
                <h3 className="text-lg font-medium text-gray-700 mb-3 mt-4">
                  Lüftung
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <CheckboxField
                    name="Fensterlüftung"
                    label="Fensterlüftung"
                    form={form}
                    required={false}
                  />

                  <CheckboxField
                    name="Schachtlüftung"
                    label="Schachtlüftung"
                    form={form}
                    required={false}
                  />

                  <CheckboxField
                    name="L_Mit_WRG"
                    label="Lüftungsanlage mit Wärmerückgewinnung"
                    form={form}
                    required={false}
                  />

                  <CheckboxField
                    name="L_Ohne_WRG"
                    label="Lüftungsanlage ohne Wärmerückgewinnung"
                    form={form}
                    required={false}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Erneuerbare Energien Felder */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Erneuerbare Energien
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <CheckboxField
                name="TW_Solar"
                label="Trinkwarmwasser-Solaranlage"
                form={form}
                required={false}
              />

              {/* Only show HZ_Solar for WG/V and NWG/V certificate types */}
              {certificateType !== "WG/B" && (
                <CheckboxField
                  name="HZ_Solar"
                  label="Heizungs-Solaranlage"
                  form={form}
                  required={false}
                />
              )}

              {/* Only show TW_WP for WG/V and NWG/V certificate types */}
              {certificateType !== "WG/B" && (
                <CheckboxField
                  name="TW_WP"
                  label="Trinkwarmwasser-Wärmepumpe"
                  form={form}
                  required={false}
                />
              )}

              {/* Only show HZ_WP for WG/V and NWG/V certificate types */}
              {certificateType !== "WG/B" && (
                <CheckboxField
                  name="HZ_WP"
                  label="Heizungs-Wärmepumpe"
                  form={form}
                  required={false}
                />
              )}
            </div>
          </div>

          {/* Dämmungsfelder für alle Zertifikatstypen */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Dämmung
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                name="Boden1_Dämmung"
                label="Boden Dämmung in cm"
                placeholder="z.B. 10"
                required={false}
              />

              <FormField
                name="Dach1_Dämmung"
                label="Dach Dämmung in cm"
                placeholder="z.B. 20"
                required={false}
              />

              <FormField
                name="Wand1_Dämmung"
                label="Wand Dämmung in cm"
                placeholder="z.B. 15"
                required={false}
              />
            </div>
          </div>

          {certificateType === "WG/B" && (
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
                Böden
              </h2>
              {getSafeArrayValue("boeden").map(
                (boden: Bauteil, index: number) => (
                  <BauteilField
                    key={boden.id}
                    index={index}
                    type="boden"
                    onRemove={(index) => removeBauteil("boden", index)}
                  />
                ),
              )}
              <button
                type="button"
                onClick={() => addBauteil("boden")}
                className="mt-2 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
              >
                Boden hinzufügen
              </button>
            </div>
          )}

          {certificateType === "WG/B" && (
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
                Dächer
              </h2>
              {getSafeArrayValue("daecher").map(
                (dach: Bauteil, index: number) => (
                  <BauteilField
                    key={dach.id}
                    index={index}
                    type="dach"
                    onRemove={(index) => removeBauteil("dach", index)}
                  />
                ),
              )}
              <button
                type="button"
                onClick={() => addBauteil("dach")}
                className="mt-2 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
              >
                Dach hinzufügen
              </button>
            </div>
          )}

          {submitError && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {submitError}
            </div>
          )}

          {/* Validation Error Summary */}
          <ValidationErrorSummary errors={validationErrors} />

          <div className="flex justify-between mt-8">
            <Link
              to="/erfassen/gebaeudedetails1"
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
            >
              Zurück
            </Link>
            <button
              type="submit"
              disabled={
                form.state.isSubmitting ||
                saveMutation.isPending ||
                isLoading ||
                !certificateType
              }
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:bg-green-300"
            >
              {form.state.isSubmitting || saveMutation.isPending
                ? "Wird gespeichert..."
                : isLoading
                  ? "Lädt..."
                  : !certificateType
                    ? "Zertifikatstyp wird geladen..."
                    : "Weiter"}
            </button>
          </div>
        </form>
      )}

      {isError && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <p>
            Fehler beim Laden der Daten:{" "}
            {error instanceof Error ? error.message : "Unbekannter Fehler"}
          </p>
          <p className="mt-2">
            Bitte versuchen Sie es später erneut oder kontaktieren Sie den
            Support.
          </p>
        </div>
      )}
    </div>
  );
};
