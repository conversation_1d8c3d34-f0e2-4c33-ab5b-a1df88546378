import React from 'react';
import { useField } from '@tanstack/react-form';

/**
 * Option interface for select field
 */
export interface SelectOption {
  value: string;
  label: string;
}

/**
 * Props interface for EnhancedSelectFieldWithCallback component
 */
export interface EnhancedSelectFieldWithCallbackProps<T> {
  name: keyof T;
  label: string;
  options: SelectOption[];
  required?: boolean;
  onChangeCallback?: (value: string) => void;
  form: any;
}

/**
 * Enhanced select field component with immediate callback execution
 * Provides real-time updates when selection changes
 */
export const EnhancedSelectFieldWithCallback = <T,>({
  name,
  label,
  options,
  required = false,
  onChangeCallback,
  form
}: EnhancedSelectFieldWithCallbackProps<T>) => {
  const { state, handleChange, handleBlur } = useField({
    name: String(name),
    form,
  });

  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    handleChange(value);
    // Call the callback immediately for real-time updates
    if (onChangeCallback) {
      onChangeCallback(value);
    }
  };

  return (
    <div className="mb-4">
      <label htmlFor={String(name)} className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <select
        id={String(name)}
        name={String(name)}
        value={String(state.value ?? '')}
        onChange={handleSelectChange}
        onBlur={handleBlur}
        className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
          state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
        }`}
        aria-invalid={state.meta.errors.length > 0}
        aria-describedby={state.meta.errors.length > 0 ? `${String(name)}-error` : undefined}
      >
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      {state.meta.errors.length > 0 && (
        <div id={`${String(name)}-error`} className="mt-1">
          <p className="text-sm text-red-600 flex items-start">
            <svg className="h-4 w-4 text-red-500 mt-0.5 mr-1 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            {state.meta.errors.join(', ')}
          </p>
        </div>
      )}
    </div>
  );
};
