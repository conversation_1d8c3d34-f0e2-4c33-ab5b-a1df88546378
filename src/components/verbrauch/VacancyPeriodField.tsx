import { useState, useEffect } from 'react';
import { useField } from '@tanstack/react-form';
import { useVacancyCalculations } from '../../hooks/useVacancyCalculations';

/**
 * Props interface for VacancyPeriodField component
 */
export interface VacancyPeriodFieldProps {
  prefix: 'ETr1' | 'ETr2' | 'ETr3';
  jahr: 'Jahr1' | 'Jahr2' | 'Jahr3';
  required?: boolean;
  form: any;
}

/**
 * Vacancy period field component with automatic percentage calculation
 * Handles vacancy period input with validation and real-time calculation
 */
export const VacancyPeriodField = ({
  prefix,
  jahr,
  required = false,
  form
}: VacancyPeriodFieldProps) => {
  const { calculateLeerstandPercentage, validateNotFutureDate, validateLeerstandDates } = useVacancyCalculations();

  // Generate field names based on prefix and year
  const hasLeerstandFieldName = `${prefix}_${jahr}_Leerstand_hasLeerstand`;
  const leerstandVonFieldName = `${prefix}_${jahr}_Lee<PERSON>and_von`;
  const leerstandBisFieldName = `${prefix}_${jahr}_Leerstand_bis`;
  const leerstandFieldName = `${prefix}_${jahr}_Leerstand`;
  const periodVonFieldName = `${prefix}_${jahr}_von`;
  const periodBisFieldName = `${prefix}_${jahr}_bis`;

  // Form field hooks
  const { state: hasLeerstandState, handleChange: handleHasLeerstandChange } = useField({
    name: String(hasLeerstandFieldName),
    form,
  });

  const { state: leerstandVonState, handleChange: handleLeerstandVonChange } = useField({
    name: String(leerstandVonFieldName),
    form,
  });

  const { state: leerstandBisState, handleChange: handleLeerstandBisChange } = useField({
    name: String(leerstandBisFieldName),
    form,
  });

  const { state: periodVonState } = useField({
    name: String(periodVonFieldName),
    form,
  });

  const { state: periodBisState } = useField({
    name: String(periodBisFieldName),
    form,
  });

  const hasLeerstand = hasLeerstandState.value === '1';

  // State for validation errors
  const [leerstandVonError, setLeerstandVonError] = useState<string | null>(null);
  const [leerstandBisError, setLeerstandBisError] = useState<string | null>(null);

  // Calculate percentage and validate dates when dates change
  const updateLeerstandPercentageAndValidation = () => {
    // Validate dates first
    if (hasLeerstand) {
      // First validate if dates are not in the future
      // validateNotFutureDate returns TRUE when date is valid (not in future)
      const isVonValid = validateNotFutureDate(leerstandVonState.value as string || '');
      const isBisValid = validateNotFutureDate(leerstandBisState.value as string || '');

      if (!isVonValid || !isBisValid) {
        setLeerstandVonError(!isVonValid ? 'Das Datum darf nicht in der Zukunft liegen' : null);
        setLeerstandBisError(!isBisValid ? 'Das Datum darf nicht in der Zukunft liegen' : null);
        return;
      }

      // Then validate the date ranges
      const { vonError, bisError } = validateLeerstandDates(
        leerstandVonState.value as string || '',
        leerstandBisState.value as string || '',
        periodVonState.value as string || '',
        periodBisState.value as string || ''
      );

      setLeerstandVonError(vonError);
      setLeerstandBisError(bisError);

      // Only calculate percentage if dates are valid and all required dates are present
      if (!vonError && !bisError && leerstandVonState.value && leerstandBisState.value &&
          periodVonState.value && periodBisState.value) {
        const percentage = calculateLeerstandPercentage(
          leerstandVonState.value as string,
          leerstandBisState.value as string,
          periodVonState.value as string,
          periodBisState.value as string
        );
        form.setFieldValue(String(leerstandFieldName), percentage.toString());
      }
    } else {
      // Clear errors and reset percentage when Leerstand is disabled
      setLeerstandVonError(null);
      setLeerstandBisError(null);
      form.setFieldValue(String(leerstandFieldName), '0');
    }
  };

  // Update percentage and validation when relevant fields change
  useEffect(() => {
    updateLeerstandPercentageAndValidation();
  }, [hasLeerstand, leerstandVonState.value, leerstandBisState.value, periodVonState.value, periodBisState.value]);

  // Set default dates when hasLeerstand is enabled and period start date is available
  useEffect(() => {
    if (hasLeerstand && periodVonState.value && !leerstandVonState.value) {
      form.setFieldValue(String(leerstandVonFieldName), periodVonState.value);
      form.setFieldValue(String(leerstandBisFieldName), periodVonState.value);
    }
  }, [hasLeerstand, periodVonState.value, leerstandVonState.value]);

  const calculatedPercentage = hasLeerstand && leerstandVonState.value && leerstandBisState.value &&
                               periodVonState.value && periodBisState.value
    ? calculateLeerstandPercentage(
        leerstandVonState.value as string,
        leerstandBisState.value as string,
        periodVonState.value as string,
        periodBisState.value as string
      )
    : 0;

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        War das Gebäude im relevanten Zeitraum leer? {required && <span className="text-red-500">*</span>}
      </label>

      <div className="space-y-3">
        <div className="flex space-x-4">
          <label className="flex items-center">
            <input
              type="radio"
              name={String(hasLeerstandFieldName)}
              value="0"
              checked={hasLeerstandState.value === '0'}
              onChange={(e) => handleHasLeerstandChange(e.target.value)}
              className="mr-2"
            />
            Nein
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name={String(hasLeerstandFieldName)}
              value="1"
              checked={hasLeerstandState.value === '1'}
              onChange={(e) => handleHasLeerstandChange(e.target.value)}
              className="mr-2"
            />
            Ja
          </label>
        </div>

        {hasLeerstand && (
          <div className="pl-4 border-l-2 border-gray-200 space-y-3">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">
                  Leerstand von
                </label>
                <input
                  type="date"
                  value={String(leerstandVonState.value ?? '')}
                  onChange={(e) => handleLeerstandVonChange(e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 transition-colors ${
                    leerstandVonError
                      ? 'border-red-500 focus:ring-red-500 focus:border-red-500 bg-red-50'
                      : 'border-gray-300 focus:ring-green-500 focus:border-green-500'
                  }`}
                  aria-invalid={!!leerstandVonError}
                  aria-describedby={leerstandVonError ? `${String(leerstandVonFieldName)}-error` : undefined}
                />
                {leerstandVonError && (
                  <p id={`${String(leerstandVonFieldName)}-error`} className="mt-1 text-sm text-red-500" role="alert">
                    {leerstandVonError}
                  </p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">
                  Leerstand bis
                </label>
                <input
                  type="date"
                  value={String(leerstandBisState.value ?? '')}
                  onChange={(e) => handleLeerstandBisChange(e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 transition-colors ${
                    leerstandBisError
                      ? 'border-red-500 focus:ring-red-500 focus:border-red-500 bg-red-50'
                      : 'border-gray-300 focus:ring-green-500 focus:border-green-500'
                  }`}
                  aria-invalid={!!leerstandBisError}
                  aria-describedby={leerstandBisError ? `${String(leerstandBisFieldName)}-error` : undefined}
                />
                {leerstandBisError && (
                  <p id={`${String(leerstandBisFieldName)}-error`} className="mt-1 text-sm text-red-500" role="alert">
                    {leerstandBisError}
                  </p>
                )}
              </div>
            </div>

            {/* Show validation errors or calculated percentage */}
            {(leerstandVonError || leerstandBisError) ? (
              <div className="bg-red-50 p-3 rounded-md">
                <p className="text-sm text-red-800">
                  <strong>Hinweis:</strong> Bitte korrigieren Sie die Datumsfehler, um den Leerstand zu berechnen.
                </p>
              </div>
            ) : calculatedPercentage > 0 && (
              <div className="bg-green-50 p-3 rounded-md">
                <p className="text-sm text-green-800">
                  <strong>Berechneter Leerstand:</strong> {calculatedPercentage.toFixed(2)}%
                </p>
                <p className="text-xs text-green-600 mt-1">
                  Dieser Wert wird automatisch basierend auf den eingegebenen Daten berechnet.
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
