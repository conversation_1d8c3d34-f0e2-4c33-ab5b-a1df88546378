import React from 'react';
import { useField } from '@tanstack/react-form';
import { getEnergySourceUnit } from '../../utils/energyCarrierUtils';

/**
 * Props interface for EnhancedConsumptionAmountField component
 */
export interface EnhancedConsumptionAmountFieldProps<T> {
  name: keyof T;
  label: string;
  placeholder?: string;
  energietraegerFieldName: keyof T;
  form: any;
  required?: boolean;
}

/**
 * Enhanced consumption amount field component with dynamic unit display
 * Displays the appropriate unit (kWh, Liter, kg) based on the selected energy carrier
 */
export const EnhancedConsumptionAmountField = <T,>({
  name,
  label,
  placeholder = '',
  energietraegerFieldName,
  form,
  required = true
}: EnhancedConsumptionAmountFieldProps<T>) => {
  const { state, handleChange, handleBlur } = useField({
    name: String(name),
    form,
  });

  // Subscribe to changes in the energy source field for dynamic unit display
  const energietraegerField = useField({
    name: String(energietraegerFieldName),
    form,
  });

  // Calculate unit dynamically based on current energy source value
  const energietraegerValue = String(energietraegerField.state.value || '');
  const dynamicUnit = getEnergySourceUnit(energietraegerValue);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    handleChange(value);
  };

  return (
    <div className="mb-4">
      <label htmlFor={String(name)} className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
        {dynamicUnit && <span className="text-gray-500"> ({dynamicUnit})</span>}
      </label>
      <div className="relative">
        <input
          id={String(name)}
          name={String(name)}
          type="text"
          value={String(state.value ?? '')}
          onChange={handleInputChange}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 ${
            state.meta.errors.length > 0 ? 'border-red-500' : 'border-gray-300'
          } ${dynamicUnit ? 'pr-16' : ''}`}
          aria-invalid={state.meta.errors.length > 0}
          aria-describedby={state.meta.errors.length > 0 ? `${String(name)}-error` : undefined}
        />
        {dynamicUnit && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <span className="text-gray-500 text-sm">{dynamicUnit}</span>
          </div>
        )}
      </div>
      {state.meta.errors.length > 0 && (
        <div id={`${String(name)}-error`} className="mt-1">
          {state.meta.errors.map((error, index) => (
            <p key={index} className="text-sm text-red-600 flex items-start">
              <svg className="h-4 w-4 text-red-500 mt-0.5 mr-1 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              {error}
            </p>
          ))}
        </div>
      )}
    </div>
  );
};
