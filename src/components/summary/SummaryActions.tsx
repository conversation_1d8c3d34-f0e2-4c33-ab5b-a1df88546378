import React from 'react';
import { useCertificate } from '../../contexts/CertificateContext';
import { usePaymentFlow } from '../../hooks/usePaymentFlow';
import type { LegalConsentState } from '../../hooks/useLegalConsent';
import { useAuth } from '../../contexts/AuthContext';
import { Link } from '@tanstack/react-router';
import { useVerbrauchData } from '../../hooks/useVerbrauchData';


interface SummaryActionsProps {
  legalConsent: LegalConsentState;
  isValid: boolean;
  onShowAccountConversion?: () => void;
}

/**
 * Action buttons component for back navigation and checkout/payment
 * Receives legal consent state from parent component
 */
export const SummaryActions: React.FC<SummaryActionsProps> = ({
  legalConsent,
  isValid,
  onShowAccountConversion
}) => {
  const { activeCertificateId, certificateData } = useCertificate();
  const { isProcessingPayment, error, handleCheckout } = usePaymentFlow(activeCertificateId, certificateData);
  const { isAnonymous } = useAuth();

  const isPaid = certificateData?.status === 'payment_complete';
  const isPaymentFailed = ['payment_failed', 'payment_disputed', 'payment_expired'].includes(certificateData?.status || '');

  const {
      certificateType
    } = useVerbrauchData();
    
  // Don't show checkout button if already paid
  const showCheckoutButton = !isPaid;

  const getCheckoutButtonText = () => {
    if (isProcessingPayment) return 'Zahlung wird verarbeitet...';
    if (isPaymentFailed) return 'Zahlung erneut versuchen';
    if (isAnonymous) return 'Konto erstellen und bezahlen';
    return 'Jetzt bezahlen';
  };

  const handleCheckoutClick = async () => {
    console.log('💳 Payment button clicked', {
      isValid,
      isAnonymous,
      isProcessingPayment,
      legalConsent,
      hasAccountConversionCallback: !!onShowAccountConversion
    });

    await handleCheckout(legalConsent, isAnonymous, () => {
      // Trigger account conversion modal for anonymous users
      console.log('🔄 Account conversion callback triggered', { isAnonymous, hasCallback: !!onShowAccountConversion });
      if (onShowAccountConversion) {
        onShowAccountConversion();
      }
    });
  };

  return (
    <div className="bg-white shadow rounded-lg p-6">
      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <Link
          to={certificateType === 'WG/B' ? '/erfassen/tww-lueftung' : '/erfassen/verbrauch'}
          className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
        >
          Zurück
        </Link>

        {/* Checkout/Payment Button */}
        {showCheckoutButton && (
          <button
            type="button"
            onClick={handleCheckoutClick}
            disabled={isProcessingPayment || !isValid}
            className={`inline-flex items-center px-8 py-2 border border-transparent text-base font-medium rounded-md text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed ${
              isValid && !isProcessingPayment
                ? 'bg-blue-600 hover:bg-blue-700'
                : 'bg-gray-400'
            }`}
          >
            {isProcessingPayment && (
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            )}
            {getCheckoutButtonText()}
          </button>
        )}

        {/* Success Message for Paid Certificates */}
        {isPaid && (
          <div className="inline-flex items-center px-6 py-3 bg-green-100 border border-green-300 rounded-md">
            <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span className="text-green-800 font-medium">
              Erfolgreich bezahlt
            </span>
          </div>
        )}
      </div>

      {/* Help Text - Only show if not paid and validation fails */}
      {!isPaid && !isValid && (
        <div className="mt-4 text-sm text-gray-600">
          <p>
            Bitte stimmen Sie allen rechtlichen Bestimmungen zu, um mit der Zahlung fortzufahren.
          </p>
        </div>
      )}
    </div>
  );
};