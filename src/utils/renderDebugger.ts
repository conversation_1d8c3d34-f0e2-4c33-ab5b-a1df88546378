import { useRef, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Comprehensive React render debugging utilities
 */

export interface RenderDebugOptions {
  componentName: string;
  logLevel?: 'minimal' | 'detailed' | 'verbose';
  trackProps?: boolean;
  trackHooks?: boolean;
  trackCallStack?: boolean;
  trackReactQuery?: boolean;
  trackAsyncOperations?: boolean;
}

export interface AsyncOperation {
  name: string;
  startTime: number;
  expectedDuration?: number;
  description?: string;
}

/**
 * Hook to track and debug React component re-renders
 * Provides detailed information about what caused each render
 */
export function useRenderDebugger(
  dependencies: Record<string, any>,
  options: RenderDebugOptions
) {
  const renderCountRef = useRef(0);
  const prevDepsRef = useRef<Record<string, any>>({});
  const renderTimestampRef = useRef<number>(Date.now());
  const asyncOperationsRef = useRef<AsyncOperation[]>([]);

  // Track React Query state if enabled
  const queryClient = options.trackReactQuery ? useQueryClient() : null;
  const prevQueryStateRef = useRef<any>(null);

  renderCountRef.current += 1;
  const currentTimestamp = Date.now();
  const timeSinceLastRender = currentTimestamp - renderTimestampRef.current;
  renderTimestampRef.current = currentTimestamp;

  // Analyze what changed
  const changes: Array<{
    key: string;
    previous: any;
    current: any;
    type: 'primitive' | 'object' | 'function' | 'array' | 'react-query';
    hasChanged: boolean;
    metadata?: any;
  }> = [];

  Object.entries(dependencies).forEach(([key, value]) => {
    const prevValue = prevDepsRef.current[key];
    const hasChanged = prevValue !== value;

    let type: 'primitive' | 'object' | 'function' | 'array' | 'react-query' = 'primitive';
    let metadata: any = undefined;

    if (typeof value === 'function') type = 'function';
    else if (Array.isArray(value)) type = 'array';
    else if (typeof value === 'object' && value !== null) {
      type = 'object';

      // Detect React Query objects
      if (value && typeof value === 'object' &&
          ('data' in value || 'isLoading' in value || 'isError' in value || 'error' in value)) {
        type = 'react-query';
        metadata = {
          hasData: 'data' in value ? !!value.data : undefined,
          isLoading: 'isLoading' in value ? value.isLoading : undefined,
          isError: 'isError' in value ? value.isError : undefined,
          status: 'status' in value ? value.status : undefined,
          fetchStatus: 'fetchStatus' in value ? value.fetchStatus : undefined,
        };
      }
    }

    changes.push({
      key,
      previous: prevValue,
      current: value,
      type,
      hasChanged,
      metadata
    });
  });

  const changedDeps = changes.filter(change => change.hasChanged);

  // Detect potential async operations based on timing patterns
  const suspiciousTimings = [83, 57, 100, 250, 500]; // Common async operation delays
  const isLikelyAsyncRender = suspiciousTimings.some(timing =>
    Math.abs(timeSinceLastRender - timing) < 10
  );

  // Log render information
  useEffect(() => {
    const { componentName, logLevel = 'detailed' } = options;

    if (logLevel === 'minimal' && changedDeps.length === 0) {
      return; // Skip logging if no changes and minimal logging
    }

    console.group(`🔍 ${componentName} render #${renderCountRef.current}`);
    console.log('Timestamp:', new Date().toISOString());
    console.log('Time since last render:', `${timeSinceLastRender}ms`);

    // Highlight suspicious timing patterns
    if (isLikelyAsyncRender) {
      console.log('⏰ SUSPICIOUS TIMING: This render timing matches common async operation delays');
    }

    if (changedDeps.length > 0) {
      console.log('🔄 Dependencies that changed:');
      changedDeps.forEach(({ key, previous, current, type, metadata }) => {
        if (type === 'react-query') {
          console.log(`  ✅ ${key} (React Query):`, {
            previous: metadata ? `data:${!!previous?.data}, loading:${previous?.isLoading}, error:${previous?.isError}` : 'undefined',
            current: metadata ? `data:${!!current?.data}, loading:${current?.isLoading}, error:${current?.isError}` : 'undefined',
            metadata,
            referenceChanged: previous !== current,
          });
        } else {
          console.log(`  ✅ ${key} (${type}):`, {
            previous,
            current,
            referenceChanged: previous !== current,
            deepEqual: type === 'object' ? JSON.stringify(previous) === JSON.stringify(current) : 'N/A'
          });
        }
      });
    } else {
      console.log('⚠️ No dependency changes detected - investigating phantom render');
      console.log('🕵️ Phantom render analysis:');
      console.log(`  - Timing: ${timeSinceLastRender}ms (suspicious: ${isLikelyAsyncRender})`);
      console.log(`  - Tracked dependencies: ${Object.keys(dependencies).length}`);

      if (logLevel === 'verbose') {
        console.log('All dependencies:', dependencies);

        if (options.trackCallStack) {
          console.log('📍 Call stack:', new Error().stack);
        }

        // Log React Query cache state if tracking is enabled
        if (options.trackReactQuery && queryClient) {
          const queryCache = queryClient.getQueryCache();
          const queries = queryCache.getAll();
          console.log('🔍 React Query cache state:', {
            totalQueries: queries.length,
            activeQueries: queries.filter(q => q.getObserversCount() > 0).length,
            staleQueries: queries.filter(q => q.isStale()).length,
            fetchingQueries: queries.filter(q => q.state.fetchStatus === 'fetching').length,
          });
        }
      }
    }

    console.groupEnd();
  });

  // Store current deps for next comparison
  prevDepsRef.current = { ...dependencies };

  return {
    renderCount: renderCountRef.current,
    timeSinceLastRender,
    changedDependencies: changedDeps,
    hasChanges: changedDeps.length > 0
  };
}

/**
 * Hook to track when a specific value changes and log detailed information
 */
export function useValueChangeTracker<T>(
  value: T,
  valueName: string,
  options: { logLevel?: 'minimal' | 'detailed'; deepCompare?: boolean } = {}
) {
  const prevValueRef = useRef<T>(value);
  const changeCountRef = useRef(0);

  const hasChanged = prevValueRef.current !== value;
  const deepEqual = options.deepCompare && typeof value === 'object' 
    ? JSON.stringify(prevValueRef.current) === JSON.stringify(value)
    : null;

  if (hasChanged) {
    changeCountRef.current += 1;
    
    if (options.logLevel !== 'minimal') {
      console.log(`🔄 ${valueName} changed (#${changeCountRef.current}):`, {
        previous: prevValueRef.current,
        current: value,
        referenceChanged: true,
        deepEqual,
        timestamp: new Date().toISOString()
      });
    }
    
    prevValueRef.current = value;
  }

  return {
    hasChanged,
    changeCount: changeCountRef.current,
    deepEqual
  };
}

/**
 * Specialized hook to track phantom renders with specific timing patterns
 * Focuses on the 83ms + 57ms delay pattern observed in the app
 */
export function usePhantomRenderTracker(componentName: string) {
  const renderTimesRef = useRef<number[]>([]);
  const renderCountRef = useRef(0);

  renderCountRef.current += 1;
  const currentTime = Date.now();
  renderTimesRef.current.push(currentTime);

  // Keep only the last 10 renders to analyze patterns
  if (renderTimesRef.current.length > 10) {
    renderTimesRef.current = renderTimesRef.current.slice(-10);
  }

  useEffect(() => {
    const renders = renderTimesRef.current;
    if (renders.length < 2) return;

    const lastRender = renders[renders.length - 1];
    const previousRender = renders[renders.length - 2];
    const timeDiff = lastRender - previousRender;

    // Check for the specific phantom render patterns
    const isPhantomPattern = [83, 57, 100, 250].some(expectedDelay =>
      Math.abs(timeDiff - expectedDelay) < 10
    );

    if (isPhantomPattern) {
      console.group(`👻 PHANTOM RENDER DETECTED: ${componentName} #${renderCountRef.current}`);
      console.log(`⏰ Timing: ${timeDiff}ms (matches phantom pattern)`);
      console.log('📍 Call stack at phantom render:');
      console.log(new Error().stack);

      // Analyze render sequence
      if (renders.length >= 3) {
        const renderDelays = [];
        for (let i = 1; i < renders.length; i++) {
          renderDelays.push(renders[i] - renders[i - 1]);
        }
        console.log('🔍 Recent render sequence:', renderDelays.map(d => `${d}ms`).join(' → '));
      }

      // Check for the specific 83ms + 57ms pattern
      if (renders.length >= 4) {
        const last3Delays = [
          renders[renders.length - 1] - renders[renders.length - 2],
          renders[renders.length - 2] - renders[renders.length - 3],
          renders[renders.length - 3] - renders[renders.length - 4]
        ];

        const matches83_57Pattern =
          (Math.abs(last3Delays[0] - 57) < 10 && Math.abs(last3Delays[1] - 83) < 10) ||
          (Math.abs(last3Delays[1] - 57) < 10 && Math.abs(last3Delays[2] - 83) < 10);

        if (matches83_57Pattern) {
          console.log('🎯 MATCHES 83ms + 57ms PHANTOM PATTERN!');
          console.log('🔍 This suggests async operations completing in sequence');
        }
      }

      console.groupEnd();
    }
  });

  return {
    renderCount: renderCountRef.current,
    renderTimes: [...renderTimesRef.current],
    lastRenderDelay: renderTimesRef.current.length >= 2
      ? renderTimesRef.current[renderTimesRef.current.length - 1] - renderTimesRef.current[renderTimesRef.current.length - 2]
      : 0
  };
}

/**
 * Utility to identify what caused a React component to re-render
 * Call this at the top of your component to get render cause analysis
 */
export function identifyRenderCause(
  componentName: string,
  props: Record<string, any> = {},
  hooks: Record<string, any> = {},
  context: Record<string, any> = {},
  options: Partial<RenderDebugOptions> = {}
) {
  const allDependencies = {
    ...Object.fromEntries(Object.entries(props).map(([key, value]) => [`prop:${key}`, value])),
    ...Object.fromEntries(Object.entries(hooks).map(([key, value]) => [`hook:${key}`, value])),
    ...Object.fromEntries(Object.entries(context).map(([key, value]) => [`context:${key}`, value]))
  };

  return useRenderDebugger(allDependencies, {
    componentName,
    logLevel: 'detailed',
    trackProps: true,
    trackHooks: true,
    trackCallStack: false,
    trackReactQuery: true,
    trackAsyncOperations: true,
    ...options
  });
}

/**
 * Simplified render cause identifier that doesn't create unstable references
 * Focuses only on essential tracking without causing phantom renders
 */
export function identifyRenderCauseSimple(
  componentName: string,
  props: Record<string, any> = {},
  hooks: Record<string, any> = {},
  context: Record<string, any> = {}
) {
  // Only track stable primitive values and avoid object references that change
  const stableDependencies = {
    ...Object.fromEntries(
      Object.entries(props)
        .filter(([, value]) => typeof value !== 'object' || value === null)
        .map(([key, value]) => [`prop:${key}`, value])
    ),
    ...Object.fromEntries(
      Object.entries(hooks)
        .filter(([, value]) => typeof value !== 'object' || value === null)
        .map(([key, value]) => [`hook:${key}`, value])
    ),
    // For contexts, only track specific stable properties
    ...Object.fromEntries(
      Object.entries(context).map(([key, value]) => {
        if (key === 'certificateContext' && value && typeof value === 'object') {
          return [`context:${key}`, value.activeCertificateId]; // Only track the ID
        }
        if (key === 'authContext' && value && typeof value === 'object') {
          return [`context:${key}`, value.isAnonymous]; // Only track stable boolean
        }
        return [`context:${key}`, value];
      })
    )
  };

  return useRenderDebugger(stableDependencies, {
    componentName,
    logLevel: 'detailed',
    trackProps: true,
    trackHooks: true,
    trackCallStack: false, // Disable to reduce noise
    trackReactQuery: false, // Disable to prevent instability
    trackAsyncOperations: false // Disable to prevent instability
  });
}
