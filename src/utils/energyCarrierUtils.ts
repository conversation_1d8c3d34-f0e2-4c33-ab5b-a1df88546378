/**
 * Energy carrier option interface
 */
export interface EnergyCarrierOption {
  value: string;
  label: string;
}

/**
 * Energy carrier type definitions
 */
export type EnergyCarrierType = 
  | 'BK_GAS' | 'BK_OEL' | 'BK_STROM' | 'BK_PELLET' | 'BK_HACKSCHNITZEL' 
  | 'BK_STUECKHOLZ' | 'BK_KOHLE' | 'BK_BIOGAS' | 'BK_BIOOEL' | 'BK_FW70' 
  | 'BK_FW50' | 'BK_FW0' | 'BK_UMWELT' | 'BK_NACHTSTR' | 'BK_HOLZ' 
  | 'BK_HHS' | 'BK_FLUESSIGGAS' | 'BK_BRAUNKOHLE';

/**
 * Energy carrier unit type
 */
export type EnergyUnit = 'kWh' | 'Liter' | 'kg';

/**
 * Available energy carrier options for selection
 */
export const energietraegerOptions: EnergyCarrierOption[] = [
  { value: '', label: 'Bitte wählen' },
  { value: 'BK_GAS', label: 'Erdgas' },
  { value: 'BK_OEL', label: 'He<PERSON>öl' },
  { value: 'BK_STROM', label: 'Strom' },
  { value: 'BK_PELLET', label: 'Holzpellets' },
  { value: 'BK_HACKSCHNITZEL', label: 'Holzhackschnitzel' },
  { value: 'BK_STUECKHOLZ', label: 'Stückholz' },
  { value: 'BK_KOHLE', label: 'Kohle' },
  { value: 'BK_BIOGAS', label: 'Biogas' },
  { value: 'BK_BIOOEL', label: 'Bioöl' },
  { value: 'BK_FW70', label: 'Fernwärme (70%)' },
  { value: 'BK_FW50', label: 'Fernwärme (50%)' },
  { value: 'BK_FW0', label: 'Fernwärme (0%)' },
  { value: 'BK_UMWELT', label: 'Umweltwärme' },
  { value: 'BK_NACHTSTR', label: 'Strom(NT)' },
  { value: 'BK_HOLZ', label: 'Holz' },
  { value: 'BK_HHS', label: 'Holzhackschnitzel' },
  { value: 'BK_FLUESSIGGAS', label: 'Flüssiggas' },
  { value: 'BK_BRAUNKOHLE', label: 'Braunkohle' },
];

/**
 * Get the appropriate unit for an energy source type
 * @param energietraegerValue - The energy carrier value
 * @returns The appropriate unit (kWh, Liter, or kg)
 */
export const getEnergySourceUnit = (energietraegerValue: string): EnergyUnit => {
  // Gaseous energy sources
  const gaseousTypes: EnergyCarrierType[] = [
    'BK_GAS', 'BK_BIOGAS', 'BK_STROM', 'BK_UMWELT', 'BK_NACHTSTR', 'BK_FLUESSIGGAS'
  ];
  
  // Liquid energy sources
  const liquidTypes: EnergyCarrierType[] = ['BK_OEL', 'BK_BIOOEL'];
  
  // Solid energy sources
  const solidTypes: EnergyCarrierType[] = [
    'BK_PELLET', 'BK_HACKSCHNITZEL', 'BK_STUECKHOLZ', 'BK_KOHLE', 
    'BK_HOLZ', 'BK_HHS', 'BK_BRAUNKOHLE'
  ];
  
  // Fernwärme types (use kWh)
  const fernwaermeTypes: EnergyCarrierType[] = ['BK_FW70', 'BK_FW50', 'BK_FW0'];

  if (gaseousTypes.includes(energietraegerValue as EnergyCarrierType) || 
      fernwaermeTypes.includes(energietraegerValue as EnergyCarrierType)) {
    return 'kWh';
  } else if (liquidTypes.includes(energietraegerValue as EnergyCarrierType)) {
    return 'Liter';
  } else if (solidTypes.includes(energietraegerValue as EnergyCarrierType)) {
    return 'kg';
  } else {
    return 'kWh'; // Default fallback
  }
};

/**
 * Get the display label for an energy carrier value
 * @param value - The energy carrier value
 * @returns The corresponding display label
 */
export const getEnergietraegerLabel = (value: string): string => {
  const option = energietraegerOptions.find(opt => opt.value === value);
  return option ? option.label : value;
};

/**
 * Check if an energy carrier is a Fernwärme type
 * @param energietraegerValue - The energy carrier value to check
 * @returns True if it's a Fernwärme type, false otherwise
 */
export const isFernwaermeType = (energietraegerValue: string): boolean => {
  const fernwaermeTypes: EnergyCarrierType[] = ['BK_FW70', 'BK_FW50', 'BK_FW0'];
  return fernwaermeTypes.includes(energietraegerValue as EnergyCarrierType);
};

/**
 * Get all energy carrier values of a specific unit type
 * @param unit - The unit type to filter by
 * @returns Array of energy carrier values that use the specified unit
 */
export const getEnergyCarriersByUnit = (unit: EnergyUnit): EnergyCarrierType[] => {
  return energietraegerOptions
    .filter(option => option.value && getEnergySourceUnit(option.value) === unit)
    .map(option => option.value as EnergyCarrierType);
};
