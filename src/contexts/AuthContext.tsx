import { createContext, useContext, useEffect, useState, useMemo, useCallback, useRef } from 'react';
import type { ReactNode } from 'react';
import type { Session, User } from '@supabase/supabase-js';
import { useQueryClient } from '@tanstack/react-query';
import { supabase, getAuthRedirectUrl } from '../lib/supabase';

type AuthContextType = {
  session: Session | null;
  user: User | null;
  loading: boolean;
  isAnonymous: boolean;
  signIn: (email: string, password: string) => Promise<{
    error: Error | null;
    data: { user: User | null; session: Session | null } | null;
  }>;
  signUp: (email: string, password: string) => Promise<{
    error: Error | null;
    data: { user: User | null; session: Session | null } | null;
  }>;
  signInAnonymously: () => Promise<{
    error: Error | null;
    data: { user: User | null; session: Session | null } | null;
  }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{
    error: Error | null;
    data: any;
  }>;
  updatePassword: (newPassword: string) => Promise<{
    error: Error | null;
    data: { user: User | null } | null;
  }>;
  linkIdentity: (email: string, password: string) => Promise<{
    error: Error | null;
    data: { user: User | null } | null;
  }>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAnonymous, setIsAnonymous] = useState(false);

  // SECURITY FIX: Get query client for cache management
  const queryClient = useQueryClient();

   // Track the current user id to avoid stale closures
  const userIdRef = useRef<string | null>(null);
  useEffect(() => {
    userIdRef.current = user?.id ?? null;
  }, [user?.id]);

  useEffect(() => {
    let mounted = true;

    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (!mounted) return;

      setSession(session);
      setUser(session?.user ?? null);
      setIsAnonymous(session?.user?.is_anonymous ?? false);
      setLoading(false);
    });

    // Listen for auth changes with debouncing to prevent excessive updates
    let timeoutId: NodeJS.Timeout;
    const { data } = supabase.auth.onAuthStateChange((event, session) => {
        if (!mounted) return;

        // Clear any pending updates
        if (timeoutId) clearTimeout(timeoutId);

        // Debounce rapid auth state changes (common on window focus)
        // Increased timeout to 250ms to properly coalesce rapid auth state changes
        // and prevent excessive re-renders when switching browser tabs
        timeoutId = setTimeout(() => {
          if (!mounted) return;

            // Optional: ignore pure token refresh events if identity hasn't changed
          if (event === 'TOKEN_REFRESHED' && session?.user?.id === userIdRef.current) {
            setLoading(false);
            return;
          }

          // Only update if identity actually changed
          if (session?.user?.id !== userIdRef.current) {
            // SECURITY FIX: Clear all cached data when user identity changes
            if (event === "SIGNED_OUT" || !session?.user) {
              // Clear all React Query caches on logout
              queryClient.clear();
            } else if (
              userIdRef.current &&
              userIdRef.current !== session?.user?.id
            ) {
              // Clear caches when different user logs in
              queryClient.clear();
            }

            setSession(session);
            setUser(session?.user ?? null);
            setIsAnonymous(session?.user?.is_anonymous ?? false);
          }

          setLoading(false);
        }, 500); // debounce - optimized for tab switching performance
      }
    );

    return () => {
      mounted = false;
      if (timeoutId) clearTimeout(timeoutId);
      data?.subscription.unsubscribe();
    };
  }, []);

  const signIn = useCallback(async (email: string, password: string) => {
    setLoading(true);
    try {
      const result = await supabase.auth.signInWithPassword({ email, password });

      // If authentication failed, reset loading state immediately
      if (result.error) {
        setLoading(false);
      }
      // If successful, onAuthStateChange will handle setting loading to false

      return result;
    } catch (error) {
      // Handle any unexpected errors
      setLoading(false);
      throw error;
    }
  }, []);
  
  const signUp = useCallback(async (email: string, password: string) => {
    setLoading(true);
    try {
      const result = await supabase.auth.signUp({ email, password });

      if (result.error) {
        setLoading(false);
      }

      return result;
    } catch (error) {
      setLoading(false);
      throw error;
    }
  }, []);

  const signInAnonymously = useCallback(async () => {
    setLoading(true);
    try {
      const result = await supabase.auth.signInAnonymously();

      if (result.error) {
        setLoading(false);
      }

      return result;
    } catch (error) {
      setLoading(false);
      throw error;
    }
  }, []);

  const signOut = useCallback(async () => {
    setLoading(true);
    try {
      // SECURITY FIX: Clear all cached data before signing out
      queryClient.clear();
      await supabase.auth.signOut();
    } finally {
      // Note: We don't set loading to false here because the onAuthStateChange will handle that
    }
  }, [queryClient]);

  const resetPassword = useCallback(async (email: string) => {
    setLoading(true);
    try {
      const baseUrl = getAuthRedirectUrl();
      // Remove any trailing slash to avoid double slashes, then add the reset-password path
      const redirectTo = `${baseUrl.replace(/\/$/, '')}/reset-password`;
      return await supabase.auth.resetPasswordForEmail(email, {
        redirectTo,
      });
    } finally {
      setLoading(false); // We set loading to false here since this doesn't trigger onAuthStateChange
    }
  }, []);

  const updatePassword = useCallback(async (newPassword: string) => {
    setLoading(true);
    try {
      return await supabase.auth.updateUser({
        password: newPassword,
      });
    } finally {
      // Note: We don't set loading to false here because the onAuthStateChange will handle that
    }
  }, []);

  const linkIdentity = useCallback(async (email: string, password: string) => {
    setLoading(true);
    try {
      // First update the user with email and password
      return await supabase.auth.updateUser({
        email,
        password,
      });
    } finally {
      // Note: We don't set loading to false here because the onAuthStateChange will handle that
    }
  }, []);

  // PHANTOM RENDER FIX: Memoize session and user objects separately to prevent reference changes
  const memoizedSession = useMemo(() => session, [
    session?.access_token,
    session?.refresh_token,
    session?.user?.id
  ]);

  const memoizedUser = useMemo(() => user, [
    user?.id,
    user?.email,
    user?.is_anonymous
  ]);

  // PHANTOM RENDER FIX: Create stable context value with memoized objects
  const value = useMemo(() => ({
    session: memoizedSession,
    user: memoizedUser,
    loading,
    isAnonymous,
    signIn,
    signUp,
    signInAnonymously,
    signOut,
    resetPassword,
    updatePassword,
    linkIdentity,
  }), [
    // PHANTOM RENDER FIX: Use memoized objects instead of raw session/user
    memoizedSession,
    memoizedUser,
    loading,
    isAnonymous,
    signIn,
    signUp,
    signInAnonymously,
    signOut,
    resetPassword,
    updatePassword,
    linkIdentity,
    queryClient
  ]);

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
