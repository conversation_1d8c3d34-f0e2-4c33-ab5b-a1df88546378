
import React, { createContext, useContext } from 'react';
import { useLegalConsent } from '../hooks/useLegalConsent';
import type { LegalConsentReturn } from '../hooks/useLegalConsent';

// 1. Create the context with a default value of null
const LegalConsentContext = createContext<LegalConsentReturn | null>(null);

// 2. Create a provider component
export const LegalConsentProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const legalConsent = useLegalConsent();
  return (
    <LegalConsentContext.Provider value={legalConsent}>
      {children}
    </LegalConsentContext.Provider>
  );
};

// 3. Create a custom hook for consuming the context
export const useLegalConsentContext = () => {
  const context = useContext(LegalConsentContext);
  if (!context) {
    throw new Error('useLegalConsentContext must be used within a LegalConsentProvider');
  }
  return context;
};
