# Payment Cancellation Flow Test

## Test Scenario
This document outlines the test steps to verify that the payment cancellation flow works correctly.

## Prerequisites
1. Development server running (`npm run dev`)
2. Supabase project configured
3. Test user account created

## Test Steps

### 1. Create a Test Certificate
1. Navigate to the application homepage
2. Start creating a new energy certificate
3. Fill out the required forms until you reach the summary page (`/erfassen/zusammenfassung`)
4. Verify the certificate status is `zusammenfassung`

### 2. Initiate Payment
1. On the summary page, click the payment button
2. Verify the certificate status changes to `payment_initiated`
3. Verify the PaymentStatusBanner shows "Zahlung wird verarbeitet" message
4. The Stripe Checkout session should open

### 3. Cancel Payment
1. In the Stripe Checkout interface, click the "Cancel" or "Back" button
2. Verify you are redirected to `/payment-cancel?certificate_id=<certificate_id>`
3. Verify the PaymentCancelPage displays the cancellation message

### 4. Verify Status Update
1. Check that the certificate status has been updated to `payment_canceled`
2. Navigate back to the summary page (`/erfassen/zusammenfassung`)
3. Verify the PaymentStatusBanner now shows "Zahlung abgebrochen" message with orange styling
4. Verify the message indicates no charges were made and payment can be retried

### 5. Test Payment Retry
1. On the summary page, click the payment button again
2. Verify the certificate status changes from `payment_canceled` to `payment_initiated`
3. Verify a new Stripe Checkout session opens successfully

### 6. Verify Admin Dashboard
1. Navigate to the admin dashboard
2. Find the test certificate in the list
3. Verify the status displays as "Zahlung abgebrochen" for canceled certificates

### 7. Verify My Certificates Page
1. Navigate to "Meine Zertifikate" page
2. Find the test certificate
3. Verify the status displays as "Zahlung abgebrochen" for canceled certificates

## Expected Results

### PaymentStatusBanner Messages
- `payment_initiated`: Yellow banner with "Zahlung wird verarbeitet"
- `payment_canceled`: Orange banner with "Zahlung abgebrochen"
- `payment_complete`: Green banner with "Zahlung erfolgreich abgeschlossen"

### Status Transitions
- `zusammenfassung` → `payment_initiated` (when payment button clicked)
- `payment_initiated` → `payment_canceled` (when user cancels in Stripe)
- `payment_canceled` → `payment_initiated` (when payment retried)
- `payment_initiated` → `payment_complete` (when payment succeeds)

### Database Updates
- Certificate status should be updated in the `energieausweise` table
- Status transitions should be logged with proper timestamps
- No duplicate status updates should occur

## Troubleshooting

### Common Issues
1. **Certificate ID not found**: Check that the cancel URL includes the certificate_id parameter
2. **Status not updating**: Check browser console for errors and verify Supabase connection
3. **Banner not showing**: Verify the PaymentStatusBanner component is included in the summary page
4. **Retry not working**: Ensure payment retry logic allows transitions from `payment_canceled`

### Debug Information
- Check browser console for log messages
- Verify localStorage contains `activeCertificateId`
- Check Supabase database for actual status values
- Verify URL parameters are correctly passed
