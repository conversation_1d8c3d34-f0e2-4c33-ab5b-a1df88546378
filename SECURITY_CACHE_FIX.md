# Security Cache Fix: User Data Isolation

## Overview

This document describes the critical security vulnerability that was discovered and fixed in the Verbrauchsausweis App related to React Query cache management and user authentication.

## Vulnerability Description

### Issue
A critical security vulnerability existed where React Query cached data was not properly invalidated when users logged out and different users logged in. This resulted in cross-user data leakage where users could see certificates belonging to previously logged-in users.

### Impact
- **Severity**: Critical
- **Data at Risk**: User certificates, personal information, building data
- **Attack Vector**: User session switching without proper cache clearing
- **Affected Users**: All users who shared devices or browsers

### Steps to Reproduce
1. Log in as user A (e.g., `<EMAIL>`)
2. Navigate to MeineZertifikatePage to view certificates
3. Log out
4. Log in as user B (e.g., `<EMAIL>`)
5. Navigate to MeineZertifikatePage
6. **Bug**: User B could see User A's certificates until manual page refresh

## Root Cause Analysis

The vulnerability was caused by two main issues:

1. **Missing Cache Invalidation on Logout**: When users logged out, the React Query cache was not cleared, allowing cached data to persist across user sessions.

2. **Non-User-Specific Query Keys**: The certificates query used a generic key `['energieausweise']` instead of user-specific keys, causing cache collisions between different users.

## Security Fix Implementation

### 1. AuthContext Cache Clearing

**File**: `src/contexts/AuthContext.tsx`

Added React Query cache clearing in two places:

```typescript
// In the signOut method
const signOut = useCallback(async () => {
  setLoading(true);
  try {
    // SECURITY FIX: Clear all cached data before signing out
    queryClient.clear();
    await supabase.auth.signOut();
  } finally {
    // Note: We don't set loading to false here because the onAuthStateChange will handle that
  }
}, [queryClient]);

// In the auth state change listener
const { data } = supabase.auth.onAuthStateChange((event, session) => {
  // ... existing code ...
  
  // Only update if identity actually changed
  if (session?.user?.id !== userIdRef.current) {
    // SECURITY FIX: Clear all cached data when user identity changes
    if (event === "SIGNED_OUT" || !session?.user) {
      // Clear all React Query caches on logout
      queryClient.clear();
    } else if (
      userIdRef.current &&
      userIdRef.current !== session?.user?.id
    ) {
      // Clear caches when different user logs in
      queryClient.clear();
    }
    
    // ... update state ...
  }
});
```

### 2. User-Specific Query Keys

**File**: `src/pages/MeineZertifikatePage.tsx`

Enhanced the certificates query to include user-specific identifiers:

```typescript
// Get current user ID for query key
const [userId, setUserId] = useState<string | null>(null);

useEffect(() => {
  const getCurrentUser = async () => {
    const { data: user } = await supabase.auth.getUser();
    setUserId(user.user?.id || null);
  };
  getCurrentUser();
}, []);

// User-specific query with user ID in key
const { data: certificates, isLoading, refetch } = useQuery({
  queryKey: ["energieausweise", "user", userId], // Include actual user ID
  queryFn: async () => {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error("Nicht eingeloggt");

    const { data, error } = await supabase
      .from("energieausweise")
      .select("*")
      .eq("user_id", user.user.id)
      .order("updated_at", { ascending: false });

    if (error) throw error;
    return data as Certificate[];
  },
  enabled: !!userId, // Only run when user ID is available
});
```

### 3. Query Invalidation Updates

Updated all query invalidation calls to use proper patterns:

```typescript
// Use wildcard pattern to invalidate all energieausweise queries
queryClient.invalidateQueries({ queryKey: ["energieausweise"] });

// For specific user data updates
queryClient.invalidateQueries({
  queryKey: ["energieausweise", "user", userId],
});
```

## Security Verification

### Test Cases
1. **User Switching Test**: Verified that logging out and logging in as different user shows only the new user's data
2. **Cache Isolation Test**: Confirmed that cached data from one user is not accessible to another user
3. **Query Key Uniqueness**: Ensured each user has unique query keys based on their user ID

### Before Fix
```
User A logs in → sees certificates A
User A logs out
User B logs in → sees certificates A (SECURITY BUG)
User B refreshes → sees certificates B
```

### After Fix
```
User A logs in → sees certificates A
User A logs out → cache cleared
User B logs in → cache cleared → sees certificates B immediately
```

## Files Modified

1. `src/contexts/AuthContext.tsx` - Added cache clearing on logout and user change
2. `src/pages/MeineZertifikatePage.tsx` - Made query keys user-specific
3. `src/pages/HomePage.tsx` - Updated query invalidation patterns
4. `src/pages/PaymentSuccessPage.tsx` - Updated query invalidation patterns

## Best Practices Implemented

1. **Defense in Depth**: Multiple layers of cache clearing (logout + user change detection)
2. **User-Specific Keys**: All user data queries now include user identifiers
3. **Proactive Clearing**: Cache is cleared before authentication operations
4. **Wildcard Invalidation**: Using query key patterns for comprehensive cache clearing

## Testing Recommendations

1. **Manual Testing**: Test user switching scenarios on shared devices/browsers
2. **Automated Testing**: Add integration tests for user session isolation
3. **Security Audits**: Regular reviews of query key patterns and cache management
4. **Browser Testing**: Test across different browsers and incognito modes

## Deployment Notes

- This is a **critical security fix** that should be deployed immediately
- No database migrations required
- No breaking changes to existing functionality
- Users may experience slightly longer load times on first login due to cache clearing (expected behavior)

## Monitoring

After deployment, monitor for:
- User complaints about seeing other users' data (should be zero)
- Performance impact from increased cache clearing
- Any authentication-related errors

## Future Improvements

1. Consider implementing per-user cache scoping at the React Query client level
2. Add automated security tests for user data isolation
3. Implement cache warming strategies to minimize performance impact
4. Consider adding user session monitoring and automatic logout on suspicious activity