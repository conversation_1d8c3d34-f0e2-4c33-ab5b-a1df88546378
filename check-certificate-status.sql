-- Query to check certificate statuses and verify payment cancellation implementation

-- Check all possible status values currently in use
SELECT 
  status,
  COUNT(*) as count,
  MIN(created_at) as first_created,
  MAX(updated_at) as last_updated
FROM energieausweise 
WHERE status IS NOT NULL
GROUP BY status
ORDER BY count DESC;

-- Check for any certificates with payment_canceled status
SELECT 
  id,
  certificate_type,
  status,
  order_number,
  created_at,
  updated_at
FROM energieausweise 
WHERE status = 'payment_canceled'
ORDER BY updated_at DESC;

-- Check certificates that might be in payment flow
SELECT 
  id,
  certificate_type,
  status,
  order_number,
  stripe_checkout_session_id,
  created_at,
  updated_at
FROM energieausweise 
WHERE status IN ('payment_initiated', 'payment_canceled', 'payment_failed', 'payment_complete')
ORDER BY updated_at DESC
LIMIT 10;

-- Verify status transitions for a specific certificate (replace with actual ID)
-- SELECT 
--   id,
--   status,
--   updated_at,
--   stripe_checkout_session_id
-- FROM energieausweise 
-- WHERE id = 'your-certificate-id-here'
-- ORDER BY updated_at DESC;
