import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.49.1";
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type"
};
serve(async (req)=>{
  // Handle CORS preflight request
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers: corsHeaders
    });
  }
  try {
    // Create a Supabase client with the Auth context of the logged in user
    const supabaseClient = createClient(Deno.env.get("SUPABASE_URL") ?? "", Deno.env.get("SUPABASE_ANON_KEY") ?? "", {
      global: {
        headers: {
          Authorization: req.headers.get("Authorization")
        }
      }
    });
    // Get the session of the logged-in user
    const { data: { session } } = await supabaseClient.auth.getSession();
    if (!session) {
      return new Response(JSON.stringify({
        error: "User not authenticated"
      }), {
        status: 401,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        }
      });
    }
    // Process the request based on the action type
    const { action, fileData } = await req.json();
    if (action === "upload") {
      const { bucketName, fileName, file } = fileData;
      // Upload file to Supabase Storage
      const { data, error } = await supabaseClient.storage.from(bucketName).upload(`${session.user.id}/${fileName}`, file, {
        upsert: true
      });
      if (error) {
        throw error;
      }
      return new Response(JSON.stringify({
        success: true,
        filePath: data.path
      }), {
        status: 200,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        }
      });
    }
    return new Response(JSON.stringify({
      error: "Invalid action"
    }), {
      status: 400,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      }
    });
  } catch (error) {
    console.error("Error:", error);
    return new Response(JSON.stringify({
      error: error.message
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      }
    });
  }
});
