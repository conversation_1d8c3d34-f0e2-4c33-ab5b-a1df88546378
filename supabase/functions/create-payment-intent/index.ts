import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@12.18.0";
// CORS headers to allow cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};
serve(async (req)=>{
  console.log("Request received:", req.method, req.url);
  // Handle CORS preflight requests - must be handled before any other logic
  if (req.method === 'OPTIONS') {
    console.log("Handling CORS preflight request");
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }
  try {
    // Check for Stripe secret key
    const stripeSecretKey = Deno.env.get("STRIPE_SECRET_KEY");
    if (!stripeSecretKey) {
      console.error("STRIPE_SECRET_KEY is not set");
      throw new Error("STRIPE_SECRET_KEY environment variable is not set");
    }
    // Initialize Stripe with the secret key
    const stripe = new Stripe(stripeSecretKey);
    // Extract request data
    const requestData = await req.json();
    const { amount, currency = 'eur', paymentMethodType = 'card', isGuest = false } = requestData;
    console.log("Request data:", {
      amount,
      currency,
      paymentMethodType,
      isGuest
    });
    if (!amount || amount < 1) {
      throw new Error('Amount is required and must be at least 1');
    }
    // IMPORTANT: For guest checkout, we need to allow requests without authentication
    // Skip authorization check completely for guest checkout
    if (!isGuest) {
      const authHeader = req.headers.get('Authorization');
      if (!authHeader) {
        console.error("No authorization header found and not a guest checkout");
        throw new Error("Missing authorization header");
      }
    // For authenticated users, we would normally validate the JWT token here
    } else {
      console.log("Processing guest checkout - no auth required");
    }
    // Create a PaymentIntent with the order amount and currency
    console.log("Creating payment intent with Stripe...");
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency,
      payment_method_types: [
        paymentMethodType
      ],
      metadata: {
        integration_check: 'stripe_payment_intent',
        is_guest: isGuest ? 'true' : 'false'
      }
    });
    console.log(`Payment intent created: ${paymentIntent.id}`);
    // Return the client secret for the payment intent
    return new Response(JSON.stringify({
      clientSecret: paymentIntent.client_secret,
      id: paymentIntent.id
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Error creating payment intent:', error);
    return new Response(JSON.stringify({
      error: error.message
    }), {
      status: 400,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
});
