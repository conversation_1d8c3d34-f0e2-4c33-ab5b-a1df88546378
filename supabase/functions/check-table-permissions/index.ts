import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.4.0";
// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};
serve(async (req)=>{
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }
  try {
    // Get Supabase credentials from environment variables
    const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("Missing Supabase credentials");
    }
    // Parse the request body
    const { table_name } = await req.json();
    if (!table_name) {
      throw new Error("Table name is required");
    }
    // Create authenticated Supabase client (using service key)
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    // Get RLS policies for the table
    const { data: policies, error: policiesError } = await supabase.from('pg_policies').select('*').eq('tablename', table_name);
    if (policiesError) {
      throw new Error(`Error getting policies: ${policiesError.message}`);
    }
    // Return the permissions check result
    return new Response(JSON.stringify({
      table: table_name,
      policies: policies || [],
      has_policies: policies && policies.length > 0 || false
    }), {
      status: 200,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error(`Error checking permissions: ${error.message}`);
    return new Response(JSON.stringify({
      error: error.message
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
});
