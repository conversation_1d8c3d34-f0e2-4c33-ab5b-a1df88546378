{"name": "verbrauchsausweis-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run"}, "dependencies": {"@heroicons/react": "^2.2.0", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-form": "^1.10.0", "@tanstack/react-query": "^5.75.5", "@tanstack/react-router": "^1.120.2", "@types/jszip": "^3.4.0", "jszip": "^3.10.1", "react": "^19.1.0", "react-dom": "^19.1.0", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/cli": "^4.1.5", "@tanstack/react-query-devtools": "^5.87.4", "@testing-library/jest-dom": "^6.8.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jsdom": "^27.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.2.4"}}